import React, { useState } from "react";
import { FaSearch } from "react-icons/fa";
import CreateCompany from "../Companies/Components/Create";
import CompaniesList from "../Companies/Companies";
import { useDebouncedSearch } from "../../hooks/useDebounceSearch";

const Dashboard = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [refreshList, setRefreshList] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 800);

  const handleDialogClose = () => {
    setDialogOpen(false);
    setRefreshList((prev) => !prev);
  };

  return (
    <div>
      <div className="p-8 ml-2">
        <div className="flex justify-between items-center mb-6 ">
          <h1 className="text-2xl font-bold ml-10">My Companies</h1>

          <div className="flex items-center space-x-4">
            {!isSearchVisible ? (
              <FaSearch
                className="text-gray-500 cursor-pointer hover:text-blue-500"
                onClick={() => setIsSearchVisible(true)}
                title="Search"
              />
            ) : (
              <div className="relative transition-all duration-300 w-64">
                <FaSearch className="absolute left-3 top-2.5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onBlur={() => setIsSearchVisible(false)}
                  className="w-full pl-10 pr-4 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                  autoFocus
                />
              </div>
            )}
            <CreateCompany open={dialogOpen} onClose={handleDialogClose} />
          </div>
        </div>
        <CompaniesList
          refresh={refreshList}
          searchTerm={debouncedSearchTerm}
          open={dialogOpen}
          onClose={handleDialogClose}
          setRefreshList={setRefreshList}
        />
      </div>
    </div>
  );
};

export default Dashboard;
