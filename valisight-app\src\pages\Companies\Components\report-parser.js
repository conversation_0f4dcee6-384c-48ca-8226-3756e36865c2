import * as pdfjsLib from "pdfjs-dist/webpack";
import { ratioMetricsPatterns, junkPatterns } from "../../../utils/patterns";
import * as diff from "diff";

const sections = {
    reportSummary: "Report Summary",
    ratioAnalysis: "Ratio Analysis",
    ratioBenchmark: "Ratio Benchmark",
    financialBenchmark: "Financial Statement Benchmark",
    nextHeading: "Current Fiscal Year",
    profitabilityRatios: "Profitability Ratios",
    glossary: "Glossary",
};

// Utility functions
const shouldExcludeLine = (str) => {
    return ratioMetricsPatterns.some((pattern) =>
        pattern.test(str.trim())
    );
};

const removeJunkData = (str, reportSummaryCount) => {
    // Handle Report Summary repetition
    if (str.includes(sections.reportSummary)) {
        if (reportSummaryCount > 0) {
            return "";
        }
        reportSummaryCount++;
    }

    str = str.replace(/[$/]/g, "");


    if (shouldExcludeLine(str)) {
        return "";
    }

    return junkPatterns.reduce((cleanedStr, pattern) => {
        return cleanedStr.replace(new RegExp(pattern, "gm"), "");
    }, str);
};

const isTitleCase = (str) => {
    return /^[A-Z][a-z]*(?:[\s-][A-Z][a-z]*)*$/.test(str);
};

const isNumericContent = (str) => {
    const numericPattern = /^[\d%.\s]+$/;
    const numericCount = (str.match(/[\d%.]/g) || []).length;
    return (
        numericPattern.test(str.trim()) || numericCount > str.length * 0.3
    );
};

export const PdfParser = async (pdfUrl) => {
    const response = await fetch(pdfUrl);
    if (!response.ok) throw new Error("Failed to fetch PDF");
    const arrayBuffer = await response.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    const numPages = pdf.numPages;
    let currentSection = null;
    let styledHTML = "";
    let isReportSummaryHeading = false;
    let hasInsertedLineBreak = false;
    let reportSummaryCount = 0;
    let sectionHeaderX = 0;
    const processedSections = new Set();

    // Process each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Skip first two pages
        if (pageNum <= 2) continue;

        // Get page dimensions
        const pageWidth = page.getViewport({ scale: 1 }).width;
        const pageMidpoint = pageWidth / 2;

        // First pass - detect section headers
        textContent.items.forEach((item) => {
            const { str, transform } = item;
            const [, , , , x] = transform;

            if (
                str.includes(sections.ratioAnalysis) ||
                str.includes(sections.ratioBenchmark)
            ) {
                sectionHeaderX = x;
            }
        });

        // Second pass - process content
        textContent.items.forEach((item) => {
            let { str, transform, fontName, width, fontStyle } = item;
            const [scaleX, skewX, skewY, scaleY, x, y] = transform;
            const isBoldOrItalic =
                fontStyle === "italic" || fontName.includes("Bold");
            const fontSize = Math.sqrt(scaleX ** 2 + skewY ** 2);
            const color = item.color || "#000";

            // Clean the string first
            str = removeJunkData(str, reportSummaryCount);

            if (shouldExcludeLine(str)) {
                return;
            }

            let newSection = null;
            if (str.includes(sections.reportSummary) && y > 300) {
                newSection = "reportSummary";
            } else if (str.includes(sections.ratioAnalysis)) {
                newSection = "ratioAnalysis";
                sectionHeaderX = x;
            } else if (str.includes(sections.ratioBenchmark)) {
                newSection = "ratioBenchmark";
                sectionHeaderX = x;
            } else if (str.includes(sections.financialBenchmark)) {
                newSection = "financialBenchmark";
            } else if (str.includes(sections.nextHeading)) {
                newSection = "nextHeading";
            } else if (str.includes(sections.profitabilityRatios)) {
                newSection = "profitabilityRatios";
            } else if (str.includes(sections.glossary)) {
                newSection = "glossary";
            }

            // Only update section if it's a new section type we haven't processed yet
            if (newSection && newSection !== currentSection) {
                if (!processedSections.has(newSection)) {
                    currentSection = newSection;
                    processedSections.add(newSection);
                }
            }

            // Skip content if we're in the Financial Statement Benchmark section
            if (
                currentSection === "financialBenchmark" ||
                currentSection == "profitabilityRatios" ||
                currentSection == "nextheading"
            ) {
                return;
            }

            // Process ratio sections (only right side content)
            if (
                (currentSection === "ratioAnalysis" ||
                    currentSection === "ratioBenchmark") &&
                str.trim() &&
                x > sectionHeaderX + 20
            ) {
                // Skip numeric content in ratio sections
                if (isNumericContent(str)) {
                    return;
                }

                if (isReportSummaryHeading && !hasInsertedLineBreak) {
                    styledHTML += `<br />`;
                    hasInsertedLineBreak = true;
                }

                // Only process right half of the page content for ratio sections
                if (x > pageMidpoint) {
                    const isHeading =
                        isBoldOrItalic || isTitleCase(str) || str.includes(":");
                    const formattedStr = isTitleCase(str)
                        ? `<strong>${str}</strong>`
                        : str;

                    styledHTML += `
                    <div style="
                    position: absolute;
                    left: ${x}px;
                    top: ${y}px;
                    font-size: ${fontSize}px;
                    font-family: ${fontName};
                    font-style: ${fontStyle || "normal"};
                    font-weight: ${isHeading ? "bold" : "normal"};
                    color: ${color};
                    width: ${width}px;
                    ">
                    ${formattedStr}
                    </div>`;
                }
            } else if (
                currentSection === "reportSummary" ||
                currentSection === "glossary"
            ) {
                if (isReportSummaryHeading && str.trim() === "1") {
                    isReportSummaryHeading = false;
                    return;
                }

                if (str.trim()) {
                    const isHeading =
                        str.includes(sections.reportSummary) ||
                        isBoldOrItalic ||
                        isTitleCase(str) ||
                        str.includes(":");
                    const formattedStr = isTitleCase(str)
                        ? `<br><strong>${str}</strong></br>`
                        : str;

                    styledHTML += `
                <div style="
                    position: absolute;
                    left: ${x}px;
                    top: ${y}px;
                    font-size: ${fontSize}px;
                    font-family: ${fontName};
                    font-style: ${fontStyle || "normal"};
                    font-weight: ${isHeading ? "bold" : "normal"};
                    color: ${color};
                    width: ${width}px;
                ">
                    ${formattedStr}
                </div>`;
                }
            }
        });
    }

    return styledHTML;
}

const processLine = (lineItems, nonBoldPhrases) => {
    let processedHTML = "";
    const junkPatterns = [
        /^•\s*/,
        /^\*\s*/,
        /^-\s*/,
        /^[\u2022\u25E6\u25CB]\s*/,
        /^o\s*/,
    ];

    // Initialize variables for line tracking
    let currentLine = "";
    let isNumberedLine = false;
    let shouldBeBold = false;
    let currentY = null;
    let currentX = null;
    let currentFontStyle = null;
    let currentFontName = null;

    lineItems.forEach((item, index) => {
        const { str, x, y, fontName, fontStyle } = item;
        let cleanStr = str.trim();

        // Apply junk pattern removal
        junkPatterns.forEach((pattern) => {
            cleanStr = cleanStr.replace(pattern, "");
        });

        if (cleanStr) {
            // Check if this is the start of a numbered line
            const isNumberStart = /^\d+\./.test(cleanStr);

            if (isNumberStart) {
                if (currentLine) {
                    processedHTML += generateLineHTML(
                        currentLine,
                        currentX,
                        currentY,
                        shouldBeBold,
                        currentFontStyle,
                        currentFontName
                    );
                }
                currentLine = cleanStr;
                isNumberedLine = true;
                shouldBeBold = true;
                currentY = y;
                currentX = x;
                currentFontStyle = fontStyle;
                currentFontName = fontName;
            } else if (currentY === null || Math.abs(y - currentY) > 2) {
                if (currentLine) {
                    processedHTML += generateLineHTML(
                        currentLine,
                        currentX,
                        currentY,
                        shouldBeBold,
                        currentFontStyle,
                        currentFontName
                    );
                }
                currentLine = cleanStr;
                isNumberedLine = false;
                shouldBeBold = isHeadingText(
                    cleanStr,
                    fontStyle,
                    fontName,
                    nonBoldPhrases
                );
                currentY = y;
                currentX = x;
                currentFontStyle = fontStyle;
                currentFontName = fontName;
            } else {
                currentLine += " " + cleanStr;
                shouldBeBold =
                    shouldBeBold ||
                    isHeadingText(cleanStr, fontStyle, fontName, nonBoldPhrases);
            }
        }
    });

    if (currentLine) {
        processedHTML += generateLineHTML(
            currentLine,
            currentX,
            currentY,
            shouldBeBold,
            currentFontStyle,
            currentFontName
        );
    }

    return processedHTML;
};

// Helper function to generate HTML for a complete line
const generateLineHTML = (
    text,
    x,
    y,
    shouldBeBold,
    fontStyle,
    fontName
) => {
    // Check if the line contains a colon
    if (text.includes(":")) {
        const [beforeColon, afterColon] = text.split(":", 2);
        const indentAmount = 20; // Indent for the second line
        const lineSpacing = 20; // Space between lines
        let html = "";

        // Bold heading before colon
        html += `<div style="left: ${x}px; top: ${y}px; font-weight: bold; color: blue;">${beforeColon}:</div>\n`;

        // Normal text after colon on next line with indent
        if (afterColon && afterColon.trim()) {
            html += `<div style="left: ${x + indentAmount}px; top: ${y + lineSpacing
                }px; color: blue;">${afterColon.trim()}</div>\n`;
        }

        return html;
    }

    // For normal lines without colon
    const style = `left: ${x}px; top: ${y}px; ${shouldBeBold ? "font-weight: bold;" : ""
        } color: blue;`;
    return `<div style="${style}">${text}</div>\n`;
};

// Helper function to determine if text should be bold
const isHeadingText = (text, fontStyle, fontName, nonBoldPhrases) => {
    if (nonBoldPhrases.some((phrase) => text.includes(phrase))) {
        return false;
    }

    return (
        /^\d+\.\s*[A-Za-z].*/.test(text) || // Numbered headings
        fontStyle === "italic" ||
        fontName.includes("Bold") ||
        /^[A-Z][a-z]*(?:\s[A-Z][a-z]*)*$/.test(text) || // All capitalized words
        text.includes(":") || // Lines with colons
        /^(?:Observation|GAAP Reference|Recommendation|Industry Reference)/.test(
            text
        ) // Specific heading types
    );
};

export const GaapPdfParser = async (pdfUrl) => {
    const response = await fetch(pdfUrl);
    if (!response.ok) throw new Error("Failed to fetch GAAP PDF");
    const arrayBuffer = await response.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    const numPages = pdf.numPages;
    let styledHTML = "";
    const textToRemove = "Now Analytics Spark Shift Energy Solutions";
    const nonBoldPhrases = [
        "Spark Shift Energy Solutions",
        "Spark",
        "Shift",
        "Energy",
        "Solutions",
    ];
    let isSummarySection = false;

    for (let pageNum = 2; pageNum <= numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        let pageText = "";
        let currentLineItems = [];

        // First pass: group items that belong to the same line
        textContent.items.forEach((item) => {
            const { str, transform } = item;
            const [, , , , x, y] = transform;

            if (
                currentLineItems.length > 0 &&
                Math.abs(currentLineItems[0].y - y) > 2
            ) {
                // Process the complete line
                const fullLine = currentLineItems
                    .map((item) => item.str)
                    .join("");
                if (!fullLine.includes(textToRemove)) {
                    pageText += processLine(currentLineItems, nonBoldPhrases);
                }
                currentLineItems = [];
            }

            currentLineItems.push({ str, x, y, ...item });
        });

        // Process any remaining items in the last line
        if (currentLineItems.length > 0) {
            const fullLine = currentLineItems.map((item) => item.str).join("");
            if (!fullLine.includes(textToRemove)) {
                pageText += processLine(currentLineItems, nonBoldPhrases);
            }
        }

        // Check for summary section
        if (pageText.toLowerCase().includes("summary")) {
            isSummarySection = true;
        }

        if (isSummarySection) {
            styledHTML += pageText;
        }
    }

    return styledHTML;
}

export const convertDivToParagraphs = (html) => {
    // Create a temporary container
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;

    // Extract all divs and replace them with <p> elements
    let output = "";
    tempDiv.querySelectorAll("div").forEach((div) => {
        let textContent = div.innerText.trim();

        if (textContent) {
            // If bold text exists, wrap it in <strong>
            if (div.innerHTML.includes("<strong>")) {
                output += `<p><strong>${textContent}</strong></p>`;
            } else {
                output += `<p>${textContent}</p>`;
            }
        }
    });

    // Replace <br /> with proper paragraph breaks
    output = output.replace(/<br\s*\/?>/gi, "<p><br></p>");

    return output;
}