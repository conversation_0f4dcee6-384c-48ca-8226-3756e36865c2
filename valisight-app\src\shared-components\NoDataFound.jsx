import React, { useState } from "react";
import { Box, Button, Typography } from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import CreateCompany from "../pages/Companies/Components/Create";

const NoCompanyFound = ({ setRefreshList }) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleDialogClose = () => {
    setDialogOpen(false);
    setRefreshList((prev) => !prev);
  };
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="60vh"
      gap={3}
      sx={{ width: "100%" }}
    >
      <Button
        variant="contained"
        size="medium"
        color="none"
        sx={{ padding: "10px" }}
        onClick={() => setDialogOpen(true)} // Open dialog on button click
      >
        <AddCircleOutlineIcon />
      </Button>

      <Typography
        variant="h5"
        sx={{
          fontWeight: 600,
          color: "#151F27",
          textAlign: "center",
        }}
      >
        Create a new company to get started
      </Typography>

      <Typography
        variant="body1"
        sx={{
          fontWeight: 400,
          color: "#475467",
          fontSize: "20px",
          textAlign: "center",
          width: "450px",
        }}
      >
        You don’t have any company yet. Create a new company to get started.
      </Typography>

      <CreateCompany onClose={handleDialogClose} />
    </Box>
  );
};

export default NoCompanyFound;
