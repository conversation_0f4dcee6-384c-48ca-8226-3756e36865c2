import cron from 'node-cron';
import { PrismaClient } from '@prisma/client';
import qboService from '../services/qbo.service.js';

const prisma = new PrismaClient();

const refreshAllQboTokens = async () => {
  try {
    const companies = await prisma.company.findMany({
      where: { qboConnectionStatus: 'CONNECTED' },
      select: { id: true, name: true },
    });

    if (companies.length === 0) {
      return;
    }

    let successCount = 0;
    let failureCount = 0;

    // Process companies sequentially to avoid overwhelming the API
    for (const company of companies) {
      try {
        const token = await qboService.getValidAccessToken(company.id);

        if (token) {
          successCount++;
        } else {
          throw new Error('No token returned from service');
        }
      } catch (error) {
        failureCount++;

        // Handle specific error cases
        if (
          error.message.includes('invalid_grant') ||
          error.message.includes('unauthorized') ||
          error.message.includes('token_expired')
        ) {
          try {
            await prisma.company.update({
              where: { id: company.id },
              data: { qboConnectionStatus: 'DISCONNECTED' },
            });
          } catch (updateError) {
            console.error(
              `[CRON] Failed to update company status for ${company.id}:`,
              updateError.message,
            );
          }
        }
      }
    }
  } catch (error) {
    console.error(
      `[CRON] Fatal error in token refresh after ${duration}ms:`,
      error.message,
    );
  }
};

cron.schedule('*/50 * * * *', async () => {
  await refreshAllQboTokens();
});

console.log('[CRON] QBO Token Refresh job scheduled (every 50 min)');
