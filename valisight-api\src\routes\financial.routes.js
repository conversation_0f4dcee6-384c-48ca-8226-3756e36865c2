import { Router } from 'express';
import * as financialController from '../controllers/financial.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

export const financialRoute = Router();

/**
 * @route GET /api/financial/comprehensive/:companyId
 * @desc Get comprehensive financial data for a company
 * @access Private
 * @params companyId (required)
 * @query accountIds[] (optional) - Array of account IDs to filter
 * @query year (optional) - Year filter
 * @query month (optional) - Month filter
 * @example /api/financial/comprehensive/123?accountIds=1&accountIds=2&year=2024&month=12
 */
financialRoute.get(
  '/comprehensive/:companyId',
  authenticate,
  financialController.getComprehensiveFinancialData,
);

/**
 * @route POST /api/financial/by-accounts
 * @desc Get financial data for specific account IDs
 * @access Private
 * @body { companyId: number, accountIds: string[] }
 * @example POST /api/financial/by-accounts
 * Body: { "companyId": 123, "accountIds": ["1", "2", "3"] }
 */
financialRoute.post(
  '/by-accounts',
  authenticate,
  financialController.getFinancialDataByAccounts,
);

/**
 * @route GET /api/financial/summary/:companyId
 * @desc Get financial summary for a company
 * @access Private
 * @params companyId (required)
 * @example /api/financial/summary/123
 */
financialRoute.get(
  '/summary/:companyId',
  authenticate,
  financialController.getFinancialSummary,
);

/**
 * @route GET /api/financial/accounts/:companyId
 * @desc Get chart of accounts with recent financial data (limited)
 * @access Private
 * @params companyId (required)
 * @query limit (optional, default: 10) - Number of accounts to return
 * @example /api/financial/accounts/123?limit=5
 */
financialRoute.get(
  '/accounts/:companyId',
  authenticate,
  financialController.getAccountsWithRecentData,
);

// ============ INDEPENDENT ENTITY ROUTES ============

/**
 * @route GET /api/financial/chart-of-accounts/:companyId
 * @desc Get Chart of Accounts data independently
 * @access Private
 * @params companyId (required)
 * @query accountIds[] (optional) - Array of account IDs to filter
 * @example /api/financial/chart-of-accounts/123?accountIds=1&accountIds=2
 */
financialRoute.get(
  '/chart-of-accounts/:companyId',
  authenticate,
  financialController.getChartOfAccounts,
);

/**
 * @route GET /api/financial/profit-loss/:companyId
 * @desc Get Profit & Loss data independently
 * @access Private
 * @params companyId (required)
 * @query accountIds[] (optional) - Array of account IDs to filter
 * @query year (optional) - Year filter
 * @query month (optional) - Month filter
 * @example /api/financial/profit-loss/123?accountIds=1&year=2024&month=12
 */
financialRoute.get(
  '/profit-loss/:companyId',
  authenticate,
  financialController.getProfitLossData,
);

/**
 * @route GET /api/financial/trial-balance/:companyId
 * @desc Get Trial Balance data independently
 * @access Private
 * @params companyId (required)
 * @query accountIds[] (optional) - Array of account IDs to filter
 * @query year (optional) - Year filter
 * @query month (optional) - Month filter
 * @example /api/financial/trial-balance/123?accountIds=1&year=2024&month=12
 */
financialRoute.get(
  '/trial-balance/:companyId',
  authenticate,
  financialController.getTrialBalanceData,
);

/**
 * @route GET /api/financial/balance-sheet/:companyId
 * @desc Get Balance Sheet data independently
 * @access Private
 * @params companyId (required)
 * @query accountIds[] (optional) - Array of account IDs to filter
 * @query year (optional) - Year filter
 * @query month (optional) - Month filter
 * @example /api/financial/balance-sheet/123?accountIds=1&year=2024&month=12
 */
financialRoute.get(
  '/balance-sheet/:companyId',
  authenticate,
  financialController.getBalanceSheetData,
);

/**
 * @route GET /api/financial/ar-aging/:companyId
 * @desc Get Accounts Receivable Aging data independently
 * @access Private
 * @params companyId (required)
 * @query year (optional) - Year filter
 * @query month (optional) - Month filter
 * @example /api/financial/ar-aging/123?year=2024&month=12
 */
financialRoute.get(
  '/ar-aging/:companyId',
  authenticate,
  financialController.getARAgingData,
);

/**
 * @route GET /api/financial/ap-aging/:companyId
 * @desc Get Accounts Payable Aging data independently
 * @access Private
 * @params companyId (required)
 * @query year (optional) - Year filter
 * @query month (optional) - Month filter
 * @example /api/financial/ap-aging/123?year=2024&month=12
 */
financialRoute.get(
  '/ap-aging/:companyId',
  authenticate,
  financialController.getAPAgingData,
);
