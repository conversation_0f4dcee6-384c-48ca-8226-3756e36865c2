import _ from 'lodash';
import { prisma } from '../db/prisma.js';
import {
  deleteFileFromS3,
  generateSignedUrl,
  uploadFileOnS3,
} from '../utils/aws-s3.js';
import { ErrorHandler } from '../utils/errorHandler.js';
import { populateEmailTemplate } from '../utils/populateEmailTemplate.js';
import { sendEmailWithTemplate } from '../utils/sendEmail.js';
import { sortAllCompanies } from '../utils/sortAllCompanies.js';

export const registerCompany = async (company_payload, userId) => {
  try {
    const company_exist = await prisma.company.findFirst({
      where: {
        name: company_payload.name,
        userId,
      },
    });

    if (company_exist) {
      return {
        status: 400,
        success: false,
        statusCode: 400,
        message: `A company with the name "${company_payload.name}" already exists for this user.`,
      };
    }
    company_payload['fiscal_year_end'] = new Date(
      company_payload.fiscal_year_end,
    );
    // company_payload['userId'] = userId;
    if (company_payload.logo) {
      const fileName = `uploads/${company_payload.name}/images/${Date.now()}-file`;
      const s3_key = await uploadFileOnS3(company_payload.logo, fileName);
      if (s3_key?.Key) {
        company_payload['logo'] = s3_key?.Key;
      }
    }

    company_payload.country = company_payload?.country || null;
    company_payload.state = company_payload?.state || null;
    company_payload.city = company_payload?.city || null;
    company_payload['market'] = company_payload.market || null;

    let newCompanyPayload = _.omit(company_payload, ['users']);

    // apply transactions
    const [company, sharedCompanies] = await prisma.$transaction(
      async (prisma) => {
        const company = await prisma.company.create({
          data: {
            ...newCompanyPayload,
            User: {
              connect: { id: userId }, // Ensure that the company is associated with the user
            },
          },
        });

        const sharedUsersPayload = company_payload.users.map((item) => ({
          userId: item,
          companyId: company.id,
        }));

        // Insert shared companies
        const sharedCompanyies = await prisma.sharedCompanies.createMany({
          data: sharedUsersPayload,
        });
        return [company, sharedCompanyies];
      },
    );

    return {
      success: true,
      statusCode: 201,
      company,
    };
  } catch (error) {
    console.log(error);

    throw {
      status: error.status,
      success: false,
      statusCode: error.statusCode,
      message: 'Error while creating the company.',
    };
  }
};

const getCompanyStatus = (companyReports) => {
  let lastRequest = companyReports[0]?.createdAt ?? '';
  let pendingFlag = false;
  let editFlag = false;
  let completedFlag = false;

  companyReports.forEach((report) => {
    if (report.status === 'download') completedFlag = true;
    else if (report.status === 'pending') pendingFlag = true;
    else if (report.status === 'edit') editFlag = true;
  });

  let status = 'pending';
  if (editFlag) status = 'edit';
  else if (!pendingFlag && completedFlag && !editFlag) status = 'download';

  return { lastRequest, status };
};

export const getAllCompanies = async (
  userId,
  page = 1,
  pageSize = 10,
  search = '',
  isAdmin,
  sortBy,
  sortOrder,
) => {
  const skip = (page - 1) * pageSize;

  try {
    // Users should see either their own companies or shared ones
    const where = isAdmin
      ? { name: { contains: search, mode: 'insensitive' } }
      : {
          OR: [
            { userId, name: { contains: search, mode: 'insensitive' } }, // Owner companies
            {
              sharedCompanies: {
                some: {
                  userId, // Companies shared with this user
                },
              },
              name: { contains: search, mode: 'insensitive' }, // Search in shared companies
            },
          ],
        };

    let allCompanies = await prisma.company.findMany({
      where,
      select: {
        id: true,
        name: true,
        fiscal_year_end: true,
        naics: true,
        description: true,
        logo: true,
        country: true,
        state: true,
        city: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
        market: true,
        naics: true,
        description: true,
        qboConnectionStatus: true,
        companyReports: {
          orderBy: { createdAt: 'desc' },
          select: { createdAt: true, status: true },
        },
        User: {
          select: { id: true, username: true, email: true },
        },
        sharedCompanies: {
          include: {
            users: { select: { id: true, username: true } },
            companies: {
              select: {
                id: true,
                name: true,
                logo: true,
                userId: true,
                fiscal_year_end: true,
                country: true,
                state: true,
                city: true,
                market: true,
                naics: true,
                description: true,
                companyReports: {
                  orderBy: { createdAt: 'desc' },
                  select: { createdAt: true, status: true },
                },
              },
            },
          },
        },
      },
    });

    let allCompaniesData = [];
    const addedCompanyIds = new Set();

    // Use for...of to handle async operations correctly
    for (const company of allCompanies) {
      const { lastRequest, status } = getCompanyStatus(company.companyReports);
      const users = await currentCompanySharedUsers(company.id, userId);

      if (!addedCompanyIds.has(company.id)) {
        allCompaniesData.push({
          id: company.id,
          name: company.name,
          owner:
            company.User.id === userId
              ? `${company.User.username} (Me)`
              : company.User.username,
          lastRequest,
          status,
          logo: company.logo,
          fiscal_year_end: company.fiscal_year_end,
          country: company.country,
          state: company.state,
          city: company.city,
          market: company.market,
          naics: company.naics,
          description: company.description,
          qboConnectionStatus: company.qboConnectionStatus,
          users,
        });
        addedCompanyIds.add(company.id);
      }

      // Add shared companies (only if not already added)
      for (const shared of company.sharedCompanies) {
        const sharedCompany = shared.companies;
        if (!sharedCompany || addedCompanyIds.has(sharedCompany.id)) continue;

        const isOwner = shared.users.id === userId;
        const { lastRequest: sharedLastRequest, status: sharedStatus } =
          getCompanyStatus(sharedCompany.companyReports);
        const users = await currentCompanySharedUsers(company.id, userId);

        allCompaniesData.push({
          id: sharedCompany.id,
          name: sharedCompany.name,
          owner: isOwner
            ? `${shared.users.username} (Me)`
            : shared.users.username,
          lastRequest: sharedLastRequest,
          status: sharedStatus,
          logo: sharedCompany.logo,
          fiscal_year_end: sharedCompany.fiscal_year_end,
          country: sharedCompany.country,
          state: sharedCompany.state,
          city: sharedCompany.city,
          market: sharedCompany.market,
          naics: sharedCompany.naics,
          description: sharedCompany.description,
          users,
        });
        addedCompanyIds.add(sharedCompany.id);
      }
    }
    // Handle logo URLs if they exist
    for (let company of allCompaniesData) {
      if (company.logo) {
        company.logo = await generateSignedUrl(company.logo);
      }
    }

    // apply sorting
    allCompaniesData = sortAllCompanies(allCompaniesData, sortBy, sortOrder);

    // Apply pagination
    let paginatedCompanies = allCompaniesData.slice(skip, skip + pageSize);

    const totalPages = Math.ceil(allCompaniesData.length / pageSize);

    return {
      success: true,
      statusCode: 200,
      companies: paginatedCompanies,
      pagination: {
        currentPage: page,
        totalPages,
        totalCompanies: allCompaniesData.length,
        pageSize,
      },
    };
  } catch (error) {
    console.error({ error });
    throw {
      status: 200,
      success: false,
      statusCode: 400,
      message: 'Error while fetching company details',
    };
  }
};

export const getCompanyDetail = async (userId, qboCompanyId, isAdmin) => {
  try {
    const companyId = Number(qboCompanyId);
    const whereClause = isAdmin
      ? { id: companyId }
      : {
          id: companyId,
          OR: [{ userId }, { sharedCompanies: { some: { userId } } }],
        };

    let companyDetail = await prisma.company.findFirst({
      where: whereClause,
      select: {
        id: true,
        name: true,
        fiscal_year_end: true,
        naics: true,
        description: true,
        logo: true,
        country: true,
        state: true,
        city: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
        market: true,
        APReportLastSyncDate: true,
        ARReportLastSyncDate: true,
        BalanceSheetReportLastSyncDate: true,
        ProfitLossReportLastSyncDate: true,
        TransactionReportLastSyncDate: true,
        qboAccessToken: true,
        qboAccessTokenCreatedAt: true,
        qboCompanyName: true,
        qboConnectionStatus: true,
        qboRealmID: true,
        qboRefreshToken: true,
        TrialBalanceReportLastSyncDate: true,
        qboTokenExpiryAt: true,
        tokenExpiryAtUtcDateTime: true,
        User: { select: { id: true, username: true, email: true } },
        companyFiles: true,
        companyReports: true,
        sharedCompanies: {
          include: {
            users: { select: { id: true, username: true, email: true } },
            companies: true,
          },
        },
      },
    });

    if (!companyDetail) {
      throw new ErrorHandler('Company not found.', 404);
    }

    if (companyDetail?.logo) {
      companyDetail['logo'] = await generateSignedUrl(companyDetail.logo);
    }

    const trial_balance_files = companyDetail.companyFiles.filter(
      (companyFile) => companyFile.file_type === 'TRIAL_BALANCE',
    );
    const chart_of_account_files = companyDetail.companyFiles.filter(
      (companyFile) => companyFile.file_type === 'CHART_OF_ACCOUNTS',
    );

    trial_balance_files.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt),
    );
    chart_of_account_files.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt),
    );

    for (let trial_balance of trial_balance_files) {
      trial_balance['file_key'] = await generateSignedUrl(
        trial_balance.file_key,
      );
    }

    for (let chart_account of chart_of_account_files) {
      chart_account['file_key'] = await generateSignedUrl(
        chart_account.file_key,
      );
    }

    companyDetail['companyFiles'] = [
      {
        type: 'CHART_OF_ACCOUNTS',
        last_updated:
          chart_of_account_files?.length > 0
            ? chart_of_account_files[0].updatedAt
            : '',
        documents: chart_of_account_files,
      },
      {
        type: 'TRIAL_BALANCE',
        last_updated:
          trial_balance_files.length > 0
            ? trial_balance_files[0]?.updatedAt
            : '',
        documents: trial_balance_files,
      },
    ];

    const usersDetails = await currentCompanySharedUsers(companyId, userId);
    companyDetail.users = usersDetails;

    return {
      success: true,
      statusCode: 200,
      company: companyDetail,
    };
  } catch (error) {
    if (error instanceof ErrorHandler) throw error;
    throw new ErrorHandler('Error while fetching company details', 500);
  }
};

export const updateCompanyDetail = async (
  userId,
  companyId,
  updateData,
  activeSharedUsers,
) => {
  try {
    if (updateData.fiscal_year_end) {
      updateData.fiscal_year_end = new Date(updateData.fiscal_year_end);
    }
    // get company
    let existingCompany = await prisma.company.findFirst({
      where: {
        AND: [
          { userId: parseInt(userId) },
          { name: updateData.name },
          {
            id: { not: parseInt(companyId) },
          },
        ],
      },
    });
    // check company
    if (existingCompany) {
      return {
        success: false,
        statusCode: 404,
        message: `Company with name ${updateData.name} already exist.`,
      };
    }

    const company_info = await prisma.company.findFirst({
      where: {
        id: parseInt(companyId),
        userId: parseInt(userId),
      },
    });

    if (updateData?.logo) {
      if (company_info?.logo) {
        await deleteFileFromS3(company_info?.logo);
      }
      const fileName = `uploads/${company_info.name}/images/${Date.now()}-file`;
      const file_metadata = await uploadFileOnS3(updateData?.logo, fileName);
      updateData['logo'] = file_metadata?.Key;
    } else {
      updateData['logo'] = company_info?.logo;
    }

    // make shared company paylaod
    let paylaod = activeSharedUsers.map((item) => ({
      userId: item,
      companyId,
    }));

    const [updatedCompany] = await prisma.$transaction([
      prisma.company.update({
        where: {
          id: companyId,
        },
        data: updateData,
      }),
      prisma.sharedCompanies.deleteMany({
        where: {
          companyId,
          // userId: { notIn: activeSharedUsers }, // Delete users who are NOT in the list
        },
      }),

      prisma.sharedCompanies.createMany({
        data: paylaod,
      }),
    ]);
    return {
      success: true,
      statusCode: 200,
      company: updatedCompany,
      message: 'Company info is successfully updated.',
    };
  } catch (error) {
    throw {
      success: false,
      statusCode: 400,
      message: error
        ? error?.message
        : 'An error occurred while trying to update the company',
    };
  }
};

export const deleteCompany = async (userId, companyId, isAdmin, email) => {
  try {
    // Fetch company details
    const company = await prisma.company.findFirst({
      where: { id: companyId },
      select: {
        id: true,
        userId: true,
        User: {
          select: {
            email: true,
          },
        },
        name: true,
        logo: true,
        sharedCompanies: {
          select: { userId: true, users: { select: { email: true } } },
        },
      },
    });

    // Validate if company exists
    if (!company) {
      return {
        success: false,
        statusCode: 404,
        message: 'Company not found.',
      };
    }

    const allMails = Array.from(
      new Set([
        ...[company.User.email],
        ...company.sharedCompanies.map((item) => item.users?.email),
        ...[email],
      ]),
    );

    console.log({ allMails });

    // Check if the user is the company owner
    const isOwner = userId === company.userId;
    if (isOwner || isAdmin === true) {
      if (company.logo) {
        await deleteFileFromS3(company.logo); // Delete logo from S3
      }

      await prisma.company.delete({ where: { id: company.id } });
    }

    // Check if the user is a shared user
    const isSharedUser = company.sharedCompanies.some(
      (shared) => shared.userId === userId,
    );

    if (isSharedUser) {
      await prisma.sharedCompanies.deleteMany({
        where: {
          userId,
          companyId,
        },
      });
    }

    // send email to user
    let replaceValues = [
      {
        original: 'companyName',
        newVal: company.name,
      },
    ];
    let body = await populateEmailTemplate('deleteCompany.html', replaceValues);
    const emailResponse = await Promise.all(
      allMails.map((email) =>
        sendEmailWithTemplate(email, `${company.name} has been archived`, body),
      ),
    );

    console.log({ emailResponse });

    return {
      success: true,
      statusCode: 200,
      message:
        isAdmin || isOwner
          ? 'Company successfully deleted.'
          : isSharedUser
            ? 'Shared company access successfully removed.'
            : 'You do not have permission to delete this company.',
    };
  } catch (error) {
    console.error('Error deleting company:', error);

    return {
      success: false,
      statusCode: 500,
      message: 'An error occurred while deleting the company.',
    };
  }
};

export const uploadCompanyInfoFiles = async (metadata, companyId, userId) => {
  try {
    if (!metadata.file) {
      return {
        success: false,
        statusCode: 400,
        success: true,
        message: 'Please select file to upload.',
      };
    }
    const { company } = await getCompanyDetail(userId, companyId);

    const fileName = `/uploads/${company.name}/files/${Date.now()}-file`;
    const file_uploaded = await uploadFileOnS3(metadata.file, fileName);

    console.log({ file_uploaded });

    if (!file_uploaded?.Key) {
      return {
        success: false,
        statusCode: 400,
        success: true,
        message: 'Error while saving file on S3.',
      };
    }

    const fileBuffer = Buffer.from(metadata.file, 'base64');
    const sizeInBytes = fileBuffer.length;

    const file_saved = await prisma.document.create({
      data: {
        file_type: metadata.type,
        size: JSON.stringify(sizeInBytes),
        file_name: metadata.name,
        file_key: file_uploaded?.Key,
        companyId: company.id,
      },
    });
    return {
      status: 200,
      success: true,
      message: 'File is uploaded successfully',
      file: file_saved,
    };
  } catch (error) {
    throw {
      status: 200,
      success: false,
      statusCode: 400,
      message: error || 'An error occurred while uploading the file',
    };
  }
};

export const getCompanyByID = async (companyID) => {
  return await prisma.company.findFirst({
    where: { id: companyID },
    include: { companyFiles: true },
  });
};

export const currentCompanySharedUsers = async (companyId, userId) => {
  // fetch all the users ids of current company shared
  let allUsersIds = await prisma.sharedCompanies.findMany({
    where: {
      companyId,
      userId: { not: null },
    },
    select: {
      users: {
        select: {
          id: true,
          username: true,
          email: true,
        },
      },
    },
  });

  return allUsersIds.map((item) => ({
    id: item.users.id,
    username: item.users.username,
    email: item.users.email,
  }));
};
