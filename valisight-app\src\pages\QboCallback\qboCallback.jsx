// frontend\src\pages\QboCallback\qboCallback.jsx
import React, { useEffect, useState, useRef, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  CircularProgress,
  Stack,
  Button,
  LinearProgress,
  Fade,
  Slide,
  Paper,
  Divider,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowForwardIcon,
} from "@mui/icons-material";
import qboButton from "../../assets/C2QB_green_btn_med_default.svg";
import axiosInstance from "../../services/axiosInstance";

const QboCallback = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [status, setStatus] = useState("processing");
  const [message, setMessage] = useState("Connecting to QuickBooks...");
  const [subMessage, setSubMessage] = useState(
    "Please wait while we establish the connection..."
  );
  const [redirectCountdown, setRedirectCountdown] = useState(null);
  const [progress, setProgress] = useState(0);
  const [connectionSteps, setConnectionSteps] = useState([
    { label: "Authenticating", completed: false, active: true },
    { label: "Exchanging tokens", completed: false, active: false },
    { label: "Verifying connection", completed: false, active: false },
    { label: "Finalizing setup", completed: false, active: false },
  ]);
  const redirectTimeoutRef = useRef(null);

  const getQueryParams = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
    return {
      code: searchParams.get("code"),
      realmId: searchParams.get("realmId"),
      state: searchParams.get("state"),
    };
  }, [location.search]);

  const getCompanyId = useCallback(
    (parsedState, data) => parsedState?.companyId || data?.companyId,
    []
  );

  const getRedirectPath = useCallback(
    (companyId) => (companyId ? `/company/${companyId}` : "/dashboard"),
    []
  );

  const updateConnectionStep = useCallback((stepIndex, completed = false) => {
    setConnectionSteps((prev) => {
      const updated = prev.map((step, index) => ({
        ...step,
        completed:
          index < stepIndex ? true : index === stepIndex ? completed : false,
        active: index === stepIndex && !completed,
      }));
      setProgress((stepIndex / (updated.length - 1)) * 100);
      return updated;
    });
  }, []);

  useEffect(() => {
    const fetchCallback = async () => {
      const { code, realmId, state } = getQueryParams();

      if (!code || !realmId || !state) {
        setStatus("error");
        setMessage("Connection Failed");
        setSubMessage("Authorization with QuickBooks failed.");
        setConnectionSteps((prev) =>
          prev.map((step) => ({ ...step, completed: false, active: false }))
        );
        setRedirectCountdown(5);
        redirectTimeoutRef.current = setTimeout(
          () => navigate("/dashboard"),
          5000
        );
        return;
      }

      // Start progress simulation
      updateConnectionStep(0, false);

      let parsedState;
      try {
        parsedState = JSON.parse(decodeURIComponent(state));
      } catch (error) {
        parsedState = null;
      }

      try {
        // Step 1: Authenticating
        setMessage("Authenticating with QuickBooks...");
        setSubMessage("Verifying your authorization credentials...");
        await new Promise((resolve) => setTimeout(resolve, 1000));
        updateConnectionStep(0, true);

        // Step 2: Exchanging tokens
        updateConnectionStep(1, false);
        setMessage("Exchanging Authorization Tokens...");
        setSubMessage("Securely exchanging tokens with QuickBooks...");

        const response = await axiosInstance.get(
          `${
            process.env.REACT_APP_API_URL
          }/api/v1/qbo/callback?code=${encodeURIComponent(
            code
          )}&realmId=${encodeURIComponent(realmId)}&state=${encodeURIComponent(
            state
          )}`
        );

        const data = response.data;
        updateConnectionStep(1, true);

        // Step 3: Verifying connection
        updateConnectionStep(2, false);
        setMessage("Verifying Connection...");
        setSubMessage("Testing the connection to your QuickBooks company...");
        await new Promise((resolve) => setTimeout(resolve, 800));

        if (response.status === 200 && data.success) {
          updateConnectionStep(2, true);

          // Step 4: Finalizing setup
          updateConnectionStep(3, false);
          setMessage("Finalizing Setup...");
          setSubMessage("Completing the integration setup...");
          await new Promise((resolve) => setTimeout(resolve, 1000));
          updateConnectionStep(3, true);

          setStatus("success");
          setMessage(
            "ValiSights Company Successfully Connected to QuickBooks!"
          );
          setSubMessage(
            "Your QuickBooks integration with ValiSights Company is now active and ready to use."
          );
          localStorage.setItem(
            "qbo_callback_success",
            "Successfully connected to QuickBooks Online!"
          );

          setRedirectCountdown(5);
          redirectTimeoutRef.current = setTimeout(() => {
            const companyId = getCompanyId(parsedState, data);
            navigate(getRedirectPath(companyId), { replace: true });
          }, 5000);
        } else {
          setStatus("error");
          setMessage("Connection Failed");
          setSubMessage(
            data.message || "Unable to establish connection with QuickBooks."
          );
          setConnectionSteps((prev) =>
            prev.map((step) => ({ ...step, completed: false, active: false }))
          );
          localStorage.setItem(
            "qbo_callback_error",
            data.message || "Error connecting to QuickBooks."
          );

          setRedirectCountdown(10);
          redirectTimeoutRef.current = setTimeout(() => {
            const companyId = parsedState?.companyId;
            navigate(getRedirectPath(companyId), { replace: true });
          }, 10000);
        }
      } catch (error) {
        // console.error("Connection error:", error);
        setStatus("error");
        setMessage("Connection Error");
        setSubMessage(
          error?.response?.data?.message ||
            "An unexpected error occurred while connecting to QuickBooks."
        );
        setConnectionSteps((prev) =>
          prev.map((step) => ({ ...step, completed: false, active: false }))
        );
        localStorage.setItem(
          "qbo_callback_error",
          "Error connecting to QuickBooks."
        );

        setRedirectCountdown(10);
        redirectTimeoutRef.current = setTimeout(() => {
          const companyId = parsedState?.companyId;
          navigate(getRedirectPath(companyId), { replace: true });
        }, 10000);
      }
    };

    fetchCallback();

    return () => {
      if (redirectTimeoutRef.current) clearTimeout(redirectTimeoutRef.current);
    };
  }, [
    location.search,
    navigate,
    getQueryParams,
    getCompanyId,
    getRedirectPath,
    updateConnectionStep,
  ]);

  useEffect(() => {
    if (redirectCountdown === null) return;
    if (redirectCountdown <= 0) return;
    const interval = setInterval(() => {
      setRedirectCountdown((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    return () => clearInterval(interval);
  }, [redirectCountdown]);

  const handleManualRedirect = (e) => {
    e.preventDefault();
    if (redirectTimeoutRef.current) clearTimeout(redirectTimeoutRef.current);
    const { state } = getQueryParams();
    let parsedState = null;
    try {
      parsedState = JSON.parse(decodeURIComponent(state));
    } catch {}
    const companyId = parsedState?.companyId;
    navigate(getRedirectPath(companyId), { replace: true });
  };

  const handleRetryConnection = () => {
    window.location.reload();
  };

  const renderIcon = () => {
    switch (status) {
      case "processing":
        return (
          <Box sx={{ position: "relative", display: "inline-flex" }}>
            <CircularProgress size={60} thickness={4} color="primary" />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: "absolute",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Box
                component="img"
                src={qboButton}
                alt="QuickBooks"
                sx={{
                  width: 24,
                  height: 24,
                  filter: "brightness(1.2)",
                }}
              />
            </Box>
          </Box>
        );
      case "success":
        return (
          <Fade in timeout={800}>
            <CheckCircleIcon
              sx={{
                fontSize: 60,
                color: "success.main",
                filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.1))",
              }}
            />
          </Fade>
        );
      case "error":
        return (
          <Fade in timeout={800}>
            <ErrorIcon
              sx={{
                fontSize: 60,
                color: "error.main",
                filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.1))",
              }}
            />
          </Fade>
        );
      default:
        return null;
    }
  };

  const renderConnectionSteps = () => {
    if (status !== "processing") return null;

    return (
      <Box sx={{ width: "100%", maxWidth: 400, mt: 3 }}>
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: "grey.200",
            "& .MuiLinearProgress-bar": {
              borderRadius: 3,
            },
          }}
        />
        <Stack spacing={1} sx={{ mt: 2 }}>
          {connectionSteps.map((step, index) => (
            <Box
              key={index}
              sx={{ display: "flex", alignItems: "center", gap: 1 }}
            >
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  borderRadius: "50%",
                  backgroundColor: step.completed
                    ? "success.main"
                    : step.active
                    ? "primary.main"
                    : "grey.300",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "all 0.3s ease",
                }}
              >
                {step.completed && (
                  <CheckCircleIcon sx={{ fontSize: 12, color: "white" }} />
                )}
                {step.active && !step.completed && (
                  <CircularProgress
                    size={10}
                    thickness={6}
                    sx={{ color: "white" }}
                  />
                )}
              </Box>
              <Typography
                variant="caption"
                sx={{
                  color:
                    step.completed || step.active
                      ? "text.primary"
                      : "text.secondary",
                  fontWeight: step.active ? 600 : 400,
                  transition: "all 0.3s ease",
                }}
              >
                {step.label}
              </Typography>
            </Box>
          ))}
        </Stack>
      </Box>
    );
  };

  return (
    <Box
      minHeight="100vh"
      display="flex"
      alignItems="center"
      justifyContent="center"
      bgcolor="#f8fafc"
      px={2}
    >
      <Slide direction="up" in timeout={600}>
        <Paper
          elevation={8}
          sx={{
            backgroundColor: "white",
            padding: { xs: 3, sm: 5 },
            borderRadius: 3,
            maxWidth: 500,
            width: "100%",
            textAlign: "center",
          }}
        >
          <Stack spacing={4} alignItems="center">
            {/* Header */}
            <Box>
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="center"
                spacing={2}
                sx={{ mb: 2 }}
              >
                <Typography variant="h6" fontWeight={600} color="primary.main">
                  ValiSights
                </Typography>
                +
                <Box
                  component="img"
                  src={qboButton}
                  alt="QuickBooks"
                  sx={{ height: 40, width: "auto" }}
                />
                <Typography
                  variant="h5"
                  fontWeight={600}
                  color="text.secondary"
                />
              </Stack>
              <Typography
                variant="h4"
                fontWeight={700}
                color="text.primary"
                sx={{ mb: 1 }}
              >
                Connect ValiSights Company to QuickBooks
              </Typography>
              <Divider
                sx={{
                  width: 80,
                  mx: "auto",
                  height: 3,
                  bgcolor: "primary.main",
                }}
              />
            </Box>

            {/* Icon */}
            {renderIcon()}

            {/* Main Message */}
            <Box sx={{ textAlign: "center" }}>
              <Typography
                variant="h6"
                fontWeight={600}
                color="text.primary"
                sx={{ mb: 1 }}
              >
                {message}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ maxWidth: 400 }}
              >
                {subMessage}
              </Typography>
            </Box>

            {/* Connection Steps */}
            {renderConnectionSteps()}

            {/* Action Buttons and Countdown */}
            {redirectCountdown !== null && redirectCountdown > 0 && (
              <Box sx={{ textAlign: "center" }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  Redirecting in {redirectCountdown} second
                  {redirectCountdown !== 1 ? "s" : ""}...
                </Typography>
                <Stack direction="row" spacing={2} justifyContent="center">
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={handleManualRedirect}
                    startIcon={<ArrowForwardIcon />}
                  >
                    Continue Now
                  </Button>
                  {status === "error" && (
                    <Button
                      variant="contained"
                      size="small"
                      onClick={handleRetryConnection}
                      startIcon={<RefreshIcon />}
                      color="primary"
                    >
                      Retry Connection
                    </Button>
                  )}
                </Stack>
              </Box>
            )}

            {/* Processing Message */}
            {status === "processing" && !redirectCountdown && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ fontStyle: "italic" }}
              >
                This may take a few moments...
              </Typography>
            )}
          </Stack>
        </Paper>
      </Slide>
    </Box>
  );
};

export default QboCallback;
