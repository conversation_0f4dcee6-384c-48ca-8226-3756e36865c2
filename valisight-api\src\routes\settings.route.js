import { Router } from "express";
export const settingsRoute = Router();
import * as settingsController from "../controllers/settings.controller.js";
import { authenticate } from "../middleware/auth.middleware.js";

settingsRoute.post("/default-users", authenticate, settingsController.getDefaultUsersList);   
settingsRoute.post("/add-share-with-user", authenticate, settingsController.AddShareWithUser);
settingsRoute.get("/shared-users", authenticate, settingsController.getSharedUsers);
settingsRoute.delete("/shared-users/:id", authenticate, settingsController.deleteSharedUser);