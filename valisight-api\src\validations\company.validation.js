import Joi from 'joi';

export const companyValidation = {
  registerCompany: Joi.object().keys({
      name: Joi.string().required().messages({
        'any.required': 'Company Name is required',
      }),
      fiscal_year_end: Joi.date().required().messages({
        'date.base': 'Fiscal year end must be a valid date.',
      }),
      naics: Joi.string().optional().messages({
        'string.base': 'NAICS must be a valid string.',
      }),
      description: Joi.string().optional().messages({
        'string.base': 'Description must be a valid string.',
      }),
      users: Joi.array().items(Joi.number().integer()).optional().messages({
        'array.base': 'Users must be an array.',
        'array.includes': 'Each user must be an integer.',
      })   
  }),
  getAllCompanies: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1).messages({
      "number.base": "Page must be a number",
      "number.integer": "Page must be an integer",
      "number.min": "Page must be at least 1",
    }),
    pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
      "number.base": "Page size must be a number",
      "number.integer": "Page size must be an integer",
      "number.min": "Page size must be at least 1",
      "number.max": "Page size cannot be more than 100",
    }),
    search: Joi.string().allow("").optional(),
  }),
  getOneCompany: Joi.object().keys({
    companyId: Joi.number().integer().positive().required().messages({
      "any.required": "Company ID is required",
      "number.base": "Company ID must be a number",
      "number.integer": "Company ID must be an integer",
      "number.positive": "Company ID must be a positive number",
    }),
  }),
  updateCompanyDetail: Joi.object().keys({
    userId: Joi.number().required().messages({
      'any.required': 'User ID is required',
      'number.base': 'User ID must be a valid number',
    }),
    companyId: Joi.number().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a valid number',
    }),
  }),
  deleteCompany: Joi.object().keys({
    userId: Joi.number().required().messages({
      'any.required': 'User ID is required',
      'number.base': 'User ID must be a valid number',
    }),
    companyId: Joi.number().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a valid number',
    }),
  }),
  updateCompanyDetail: Joi.object().keys({
    userId: Joi.number().required().messages({
      'any.required': 'User ID is required',
      'number.base': 'User ID must be a valid number',
    }),
    companyId: Joi.number().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a valid number',
    }),
  }),
  deleteCompany: Joi.object().keys({
    userId: Joi.number().required().messages({
      'any.required': 'User ID is required',
      'number.base': 'User ID must be a valid number',
    }),
    companyId: Joi.number().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a valid number',
    }),
  }),
};
