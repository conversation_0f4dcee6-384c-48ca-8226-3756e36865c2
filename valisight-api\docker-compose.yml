services:
  db:
    image: postgres:15-alpine
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - internal_network  # Connect to internal network

  backend:
    build: .
    # Remove port mapping since Traefik handles it
    # ports:
    #   - "8080:8000"  # Host:Container
    env_file:
      - .env
    depends_on:
      db:
        condition: service_healthy
    restart: always
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.fintuition.ai`)"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=myresolver"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"  # Port inside the container where the app is running
      - "traefik.docker.network=traefik_network"
    networks:
      - traefik_network   # For external access via Traefik
      - internal_network  # For internal communication with `db`

  # Database Backup Service
  db_backup:
    image: kartoza/pg-backup:17-3.5
    container_name: postgres_db_backup
    env_file:
      - .env  # Contains database credentials and other environment variables
    environment:
      POSTGRES_HOST: db
      POSTGRES_PORT: 5432
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASS: ${POSTGRES_PASSWORD}
      POSTGRES_DBNAME: ${POSTGRES_DB}
      DUMPPREFIX: "PG"
      CRON_SCHEDULE: "0 2 * * *" # Run daily at 2 AM
      # REMOVE_BEFORE: 7  # Keep backups for 7 days
    volumes:
      - ./backups:/backups  # Mount the host backups directory
    depends_on:
      - db
    restart: always
    networks:
      - internal_network  # Connect to internal network

volumes:
  db-data:

networks:
  traefik_network:
    external: true
  internal_network:
    driver: bridge
