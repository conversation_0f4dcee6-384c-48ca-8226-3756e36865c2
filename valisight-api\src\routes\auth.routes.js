import { Router } from 'express';
export const authRoute = Router();
import * as authController from '../controllers/auth.controller.js';

authRoute.post('/register', authController.register);

authRoute.post('/login', authController.login);

authRoute.post('/forget-password', authController.forgetPassword);
authRoute.post('/reset-password-email', authController.resetPasswordEmail);
authRoute.post('/reset-password/:token', authController.resetPassword);
