-- CreateEnum
CREATE TYPE "QboConnectionStatus" AS ENUM ('CONNECTED', 'DISCONNECTED');

-- Create<PERSON>num
CREATE TYPE "ReportTemplate" AS ENUM ('DEEPSIGHT');

-- CreateEnum
CREATE TYPE "TemplateType" AS ENUM ('GLOBAL', 'CUSTOM');

-- AlterTable
ALTER TABLE "Company" ADD COLUMN     "APReportLastSyncDate" DATE,
ADD COLUMN     "ARReportLastSyncDate" DATE,
ADD COLUMN     "BalanceSheetReportLastSyncDate" DATE,
ADD COLUMN     "ProfitLossReportLastSyncDate" DATE,
ADD COLUMN     "TransactionReportLastSyncDate" DATE,
ADD COLUMN     "TrialBalanceReportLastSyncDate" DATE,
ADD COLUMN     "market" TEXT,
ADD COLUMN     "qboAccessToken" TEXT,
ADD COLUMN     "qboAccessTokenCreatedAt" TIMESTAMP(3),
ADD COLUMN     "qboCompanyName" TEXT,
ADD COLUMN     "qboConnectionStatus" "QboConnectionStatus" NOT NULL DEFAULT 'DISCONNECTED',
ADD COLUMN     "qboRealmID" TEXT,
ADD COLUMN     "qboRefreshToken" TEXT NOT NULL DEFAULT 'null',
ADD COLUMN     "qboTokenExpiryAt" TIMESTAMP(6),
ADD COLUMN     "tokenExpiryAtUtcDateTime" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "ReportRequest" ADD COLUMN     "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "text" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "SharedCompanies" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "companyId" INTEGER,

    CONSTRAINT "SharedCompanies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DefaultUserSettings" (
    "id" SERIAL NOT NULL,
    "username" TEXT NOT NULL,
    "sharedBy" INTEGER,
    "sharedWith" INTEGER,
    "deleted" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "DefaultUserSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Account" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "connectionId" INTEGER,
    "name" VARCHAR(255) NOT NULL,
    "fullyQualifiedAccountName" VARCHAR(500) NOT NULL,
    "type" VARCHAR(100) NOT NULL,
    "isActiveAccount" BOOLEAN,
    "currencyCode" VARCHAR(10),
    "accountSubTypeName" VARCHAR(100),
    "accountClassification" VARCHAR(100),
    "currentAccountBalance" DECIMAL(15,2),
    "isSubAccountFlag" BOOLEAN,
    "parentAccountQuickbooksId" VARCHAR(100),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TemplateSettings" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "reportType" "ReportTemplate" NOT NULL,
    "templateType" "TemplateType" NOT NULL DEFAULT 'GLOBAL',
    "settings" JSONB NOT NULL,
    "updatedBy" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TemplateSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReportContentSettings" (
    "id" SERIAL NOT NULL,
    "companyId" INTEGER NOT NULL,
    "reportType" "ReportTemplate" NOT NULL,
    "chartSettings" JSONB NOT NULL,
    "promptDescription" TEXT NOT NULL,
    "updatedBy" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportContentSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AccountPayableAgingSummaryReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "realmId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "total" DECIMAL(15,2) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AccountPayableAgingSummaryReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AccountReceivableAgingSummaryReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "realmId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "total" DECIMAL(15,2) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AccountReceivableAgingSummaryReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfitLossReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "currencyCode" VARCHAR(10) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProfitLossReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BalanceSheetReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "statementAmount" DECIMAL(15,2) NOT NULL,
    "currencyCode" VARCHAR(10) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BalanceSheetReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrialBalanceReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "monthEndDebitAmount" DECIMAL(15,2) NOT NULL,
    "monthEndCreditAmount" DECIMAL(15,2) NOT NULL,
    "netChangeAmount" DECIMAL(15,2) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TrialBalanceReport_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SharedCompanies_userId_companyId_key" ON "SharedCompanies"("userId", "companyId");

-- CreateIndex
CREATE UNIQUE INDEX "DefaultUserSettings_sharedBy_sharedWith_key" ON "DefaultUserSettings"("sharedBy", "sharedWith");

-- CreateIndex
CREATE INDEX "Account_userId_idx" ON "Account"("userId");

-- CreateIndex
CREATE INDEX "Account_realmId_idx" ON "Account"("realmId");

-- CreateIndex
CREATE INDEX "Account_accountId_idx" ON "Account"("accountId");

-- CreateIndex
CREATE INDEX "Account_connectionId_idx" ON "Account"("connectionId");

-- CreateIndex
CREATE INDEX "Account_type_idx" ON "Account"("type");

-- CreateIndex
CREATE UNIQUE INDEX "Account_accountId_userId_realmId_key" ON "Account"("accountId", "userId", "realmId");

-- CreateIndex
CREATE INDEX "TemplateSettings_userId_idx" ON "TemplateSettings"("userId");

-- CreateIndex
CREATE INDEX "TemplateSettings_templateType_idx" ON "TemplateSettings"("templateType");

-- CreateIndex
CREATE INDEX "TemplateSettings_reportType_idx" ON "TemplateSettings"("reportType");

-- CreateIndex
CREATE UNIQUE INDEX "TemplateSettings_userId_reportType_templateType_key" ON "TemplateSettings"("userId", "reportType", "templateType");

-- CreateIndex
CREATE INDEX "ReportContentSettings_companyId_idx" ON "ReportContentSettings"("companyId");

-- CreateIndex
CREATE INDEX "ReportContentSettings_reportType_idx" ON "ReportContentSettings"("reportType");

-- CreateIndex
CREATE INDEX "ReportContentSettings_updatedBy_idx" ON "ReportContentSettings"("updatedBy");

-- CreateIndex
CREATE UNIQUE INDEX "ReportContentSettings_companyId_reportType_key" ON "ReportContentSettings"("companyId", "reportType");

-- CreateIndex
CREATE INDEX "AccountPayableAgingSummaryReport_userId_idx" ON "AccountPayableAgingSummaryReport"("userId");

-- CreateIndex
CREATE INDEX "AccountPayableAgingSummaryReport_realmId_idx" ON "AccountPayableAgingSummaryReport"("realmId");

-- CreateIndex
CREATE INDEX "AccountPayableAgingSummaryReport_year_month_idx" ON "AccountPayableAgingSummaryReport"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "AccountPayableAgingSummaryReport_userId_realmId_year_month_key" ON "AccountPayableAgingSummaryReport"("userId", "realmId", "year", "month");

-- CreateIndex
CREATE INDEX "AccountReceivableAgingSummaryReport_userId_idx" ON "AccountReceivableAgingSummaryReport"("userId");

-- CreateIndex
CREATE INDEX "AccountReceivableAgingSummaryReport_realmId_idx" ON "AccountReceivableAgingSummaryReport"("realmId");

-- CreateIndex
CREATE INDEX "AccountReceivableAgingSummaryReport_year_month_idx" ON "AccountReceivableAgingSummaryReport"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "AccountReceivableAgingSummaryReport_userId_realmId_year_mon_key" ON "AccountReceivableAgingSummaryReport"("userId", "realmId", "year", "month");

-- CreateIndex
CREATE INDEX "ProfitLossReport_userId_idx" ON "ProfitLossReport"("userId");

-- CreateIndex
CREATE INDEX "ProfitLossReport_realmId_idx" ON "ProfitLossReport"("realmId");

-- CreateIndex
CREATE INDEX "ProfitLossReport_accountId_idx" ON "ProfitLossReport"("accountId");

-- CreateIndex
CREATE INDEX "ProfitLossReport_year_month_idx" ON "ProfitLossReport"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "ProfitLossReport_userId_realmId_year_month_accountId_key" ON "ProfitLossReport"("userId", "realmId", "year", "month", "accountId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_userId_idx" ON "BalanceSheetReport"("userId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_realmId_idx" ON "BalanceSheetReport"("realmId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_accountId_idx" ON "BalanceSheetReport"("accountId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_year_month_idx" ON "BalanceSheetReport"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "BalanceSheetReport_userId_realmId_year_month_accountId_key" ON "BalanceSheetReport"("userId", "realmId", "year", "month", "accountId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_userId_idx" ON "TrialBalanceReport"("userId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_realmId_idx" ON "TrialBalanceReport"("realmId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_accountId_idx" ON "TrialBalanceReport"("accountId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_year_month_idx" ON "TrialBalanceReport"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "TrialBalanceReport_userId_realmId_year_month_accountId_key" ON "TrialBalanceReport"("userId", "realmId", "year", "month", "accountId");

-- AddForeignKey
ALTER TABLE "SharedCompanies" ADD CONSTRAINT "SharedCompanies_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SharedCompanies" ADD CONSTRAINT "SharedCompanies_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DefaultUserSettings" ADD CONSTRAINT "DefaultUserSettings_sharedBy_fkey" FOREIGN KEY ("sharedBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DefaultUserSettings" ADD CONSTRAINT "DefaultUserSettings_sharedWith_fkey" FOREIGN KEY ("sharedWith") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TemplateSettings" ADD CONSTRAINT "TemplateSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TemplateSettings" ADD CONSTRAINT "TemplateSettings_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReportContentSettings" ADD CONSTRAINT "ReportContentSettings_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReportContentSettings" ADD CONSTRAINT "ReportContentSettings_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountPayableAgingSummaryReport" ADD CONSTRAINT "AccountPayableAgingSummaryReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountReceivableAgingSummaryReport" ADD CONSTRAINT "AccountReceivableAgingSummaryReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfitLossReport" ADD CONSTRAINT "ProfitLossReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfitLossReport" ADD CONSTRAINT "ProfitLossReport_accountId_userId_realmId_fkey" FOREIGN KEY ("accountId", "userId", "realmId") REFERENCES "Account"("accountId", "userId", "realmId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BalanceSheetReport" ADD CONSTRAINT "BalanceSheetReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BalanceSheetReport" ADD CONSTRAINT "BalanceSheetReport_accountId_userId_realmId_fkey" FOREIGN KEY ("accountId", "userId", "realmId") REFERENCES "Account"("accountId", "userId", "realmId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrialBalanceReport" ADD CONSTRAINT "TrialBalanceReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrialBalanceReport" ADD CONSTRAINT "TrialBalanceReport_accountId_userId_realmId_fkey" FOREIGN KEY ("accountId", "userId", "realmId") REFERENCES "Account"("accountId", "userId", "realmId") ON DELETE RESTRICT ON UPDATE CASCADE;
