openapi: 3.0.3
info:
  title: Valisight API
  description: Comprehensive financial reporting and company management system API
  version: 1.0.0
  contact:
    name: Valisight Development Team
servers:
  - url: http://localhost:8000/api/v1
    description: Development server
  - url: https://api.valisight.com/api/v1
    description: Production server

security:
  - BearerAuth: []

paths:
  # Authentication Endpoints
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
                - email
              properties:
                username:
                  type: string
                  description: Username for the account
                password:
                  type: string
                  description: Password for the account
                email:
                  type: string
                  format: email
                  description: Email address
      responses:
        '200':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                success: true
                data:
                  id: 1
                  username: "john_doe"
                  email: "<EMAIL>"

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and get JWT token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'

  /auth/forget-password:
    post:
      tags:
        - Authentication
      summary: Forget password
      description: Initiate password reset process
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Password reset email sent
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /auth/reset-password-email:
    post:
      tags:
        - Authentication
      summary: Send reset password email
      description: Send password reset email to user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Reset password email sent
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /auth/reset-password/{token}:
    post:
      tags:
        - Authentication
      summary: Reset password
      description: Reset password using token
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
          description: JWT reset token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - password
                - confirmPassword
              properties:
                password:
                  type: string
                confirmPassword:
                  type: string
                isPasswordReset:
                  type: boolean
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # Company Endpoints
  /company:
    post:
      tags:
        - Company
      summary: Register company
      description: Create a new company
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyCreate'
      responses:
        '200':
          description: Company created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyResponse'

  /company/all:
    get:
      tags:
        - Company
      summary: Get all companies
      description: Get paginated list of companies for authenticated user
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
            minimum: 1
          description: Page number
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
          description: Items per page
        - name: search
          in: query
          schema:
            type: string
          description: Search term for company name
      responses:
        '200':
          description: Companies retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyListResponse'

  /company/{id}:
    get:
      tags:
        - Company
      summary: Get company detail
      description: Get specific company details
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Company ID
      responses:
        '200':
          description: Company details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyDetailResponse'

    put:
      tags:
        - Company
      summary: Update company
      description: Update company details
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Company ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUpdate'
      responses:
        '200':
          description: Company updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyResponse'

    delete:
      tags:
        - Company
      summary: Delete company
      description: Delete a company
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Company ID
      responses:
        '200':
          description: Company deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /company/{id}/upload-file:
    post:
      tags:
        - Company
      summary: Upload company files
      description: Upload files for a company
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Company ID
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        '200':
          description: Files uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /company/location:
    get:
      tags:
        - Company
      summary: Get company location
      description: Get company location information
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Company location retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LocationResponse'

  # Report Endpoints
  /report:
    post:
      tags:
        - Report
      summary: Register report request
      description: Create a new report request
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportCreate'
      responses:
        '200':
          description: Report request created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportResponse'

  /report/{id}:
    get:
      tags:
        - Report
      summary: Get report by ID
      description: Get specific report details
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Report ID
      responses:
        '200':
          description: Report details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportResponse'

    delete:
      tags:
        - Report
      summary: Delete report request
      description: Delete a report request
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Report ID
      responses:
        '200':
          description: Report deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /report/{id}/editable:
    post:
      tags:
        - Report
      summary: Save edited report
      description: Save edited report request
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Report ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                edited_data:
                  type: string
      responses:
        '200':
          description: Report saved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /report/{id}/all:
    get:
      tags:
        - Report
      summary: Get all report requests
      description: Get all reports for a company
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Company ID
      responses:
        '200':
          description: Reports retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportListResponse'

  /report/{id}/cashflow/upload:
    post:
      tags:
        - Report
      summary: Upload cashflow file
      description: Upload cashflow file for a report
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Report ID
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - company_id
              properties:
                company_id:
                  type: integer
                  description: Company ID
                  example: 1
                file:
                  type: string
                  format: binary
                  description: Cashflow file to upload
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /report/{id}/non-cashflow/upload:
    post:
      tags:
        - Report
      summary: Upload non-cashflow file
      description: Upload non-cashflow file for a report
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Report ID
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - company_id
              properties:
                company_id:
                  type: integer
                  description: Company ID
                  example: 1
                file:
                  type: string
                  format: binary
                  description: Non-cashflow file to upload
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /report/edited-file/{id}/upload:
    post:
      tags:
        - Report
      summary: Upload edited file for non-cashflow
      description: Upload edited file for non-cashflow report
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Report ID
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - company_id
              properties:
                company_id:
                  type: integer
                  description: Company ID
                  example: 1
                file:
                  type: string
                  format: binary
                  description: Edited file to upload
      responses:
        '200':
          description: Edited file uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /report/download:
    get:
      tags:
        - Report
      summary: Download file
      description: Download report files
      parameters:
        - name: id
          in: query
          required: true
          schema:
            type: integer
          description: Report ID
      responses:
        '200':
          description: File download
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary

  /report/generate-report-link/{key}:
    get:
      tags:
        - Report
      summary: Generate report link
      description: Generate public link for report access
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Report access key
      responses:
        '200':
          description: Report link generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  url:
                    type: string

  # Template Endpoints
  /templates:
    post:
      tags:
        - Template
      summary: Create template
      description: Create a new template with files
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Template name
                description:
                  type: string
                  description: Template description
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  maxItems: 5
      responses:
        '200':
          description: Template created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'

    get:
      tags:
        - Template
      summary: Get templates
      description: Get templates for authenticated user
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Templates retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'

  /templates/all:
    get:
      tags:
        - Template
      summary: Get all templates
      description: Get all templates (admin access)
      responses:
        '200':
          description: All templates retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'

  /templates/{id}/download:
    get:
      tags:
        - Template
      summary: Download template files
      description: Download template files
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Template ID
      responses:
        '200':
          description: Template files download
          content:
            application/zip:
              schema:
                type: string
                format: binary

  /templates/{id}:
    delete:
      tags:
        - Template
      summary: Delete template
      description: Delete a template
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Template ID
      responses:
        '200':
          description: Template deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /templates/{templateId}:
    put:
      tags:
        - Template
      summary: Update template
      description: Update template with new files
      parameters:
        - name: templateId
          in: path
          required: true
          schema:
            type: integer
          description: Template ID
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Template name
                description:
                  type: string
                  description: Template description
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  maxItems: 5
      responses:
        '200':
          description: Template updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'

  # FAQ Endpoints
  /faqs:
    post:
      tags:
        - FAQ
      summary: Create FAQ
      description: Create a new FAQ entry
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FAQCreate'
      responses:
        '200':
          description: FAQ created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FAQResponse'

    get:
      tags:
        - FAQ
      summary: Get FAQs
      description: Get FAQs for authenticated user
      security:
        - BearerAuth: []
      responses:
        '200':
          description: FAQs retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FAQListResponse'

  /faqs/all:
    get:
      tags:
        - FAQ
      summary: Get all FAQs
      description: Get all FAQs (admin access)
      responses:
        '200':
          description: All FAQs retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FAQListResponse'

  /faqs/{ids}:
    delete:
      tags:
        - FAQ
      summary: Delete FAQ
      description: Delete FAQ entries
      parameters:
        - name: ids
          in: path
          required: true
          schema:
            type: string
          description: Comma-separated FAQ IDs
      responses:
        '200':
          description: FAQs deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /faqs/{id}:
    put:
      tags:
        - FAQ
      summary: Update FAQ
      description: Update FAQ entry
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: FAQ ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FAQUpdate'
      responses:
        '200':
          description: FAQ updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FAQResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"

    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            token:
              type: string
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            user:
              type: object
              properties:
                id:
                  type: integer
                  example: 1
                username:
                  type: string
                  example: "john_doe"
                email:
                  type: string
                  example: "<EMAIL>"

    CompanyCreate:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Acme Corp"
        fiscal_year_end:
          type: string
          format: date
          example: "2024-12-31"
        description:
          type: string
          example: "Technology company"
        naics:
          type: string
          example: "511210"
        country:
          type: string
          example: "USA"
        state:
          type: string
          example: "California"
        city:
          type: string
          example: "San Francisco"
        logo:
          type: string
          format: base64
          description: "Base64 encoded logo file"

    CompanyUpdate:
      type: object
      properties:
        name:
          type: string
        fiscal_year_end:
          type: string
          format: date
        description:
          type: string
        naics:
          type: string
        country:
          type: string
        state:
          type: string
        city:
          type: string
        logo:
          type: string
          format: base64

    CompanyResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        statusCode:
          type: integer
          example: 201
        company:
          type: object
          properties:
            id:
              type: integer
              example: 1
            name:
              type: string
              example: "Acme Corp"
            fiscal_year_end:
              type: string
              format: date
              example: "2024-12-31"
            description:
              type: string
              example: "Technology company"
            naics:
              type: string
              example: "511210"
            userId:
              type: integer
              example: 1

    CompanyDetailResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        statusCode:
          type: integer
          example: 200
        company:
          type: object
          properties:
            id:
              type: integer
              example: 1
            name:
              type: string
              example: "Acme Corp"
            fiscal_year_end:
              type: string
              format: date
              example: "2024-12-31"
            description:
              type: string
              example: "Technology company"
            naics:
              type: string
              example: "511210"
            userId:
              type: integer
              example: 1
            logo:
              type: string
              example: "https://s3.amazonaws.com/..."
            companyFiles:
              type: array
              items:
                type: object
                properties:
                  type:
                    type: string
                    enum: ["CHART_OF_ACCOUNTS", "TRIAL_BALANCE"]
                  last_updated:
                    type: string
                    format: date-time
                  documents:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                        file_name:
                          type: string
                        file_key:
                          type: string
                        file_type:
                          type: string
                        size:
                          type: string
                        createdAt:
                          type: string
                          format: date-time

    CompanyListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        statusCode:
          type: integer
          example: 200
        companies:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              name:
                type: string
                example: "Acme Corp"
              fiscal_year_end:
                type: string
                format: date
                example: "2024-12-31"
              description:
                type: string
                example: "Technology company"
              naics:
                type: string
                example: "511210"
              logo:
                type: string
                example: "https://s3.amazonaws.com/..."
        pagination:
          type: object
          properties:
            currentPage:
              type: integer
              example: 1
            totalPages:
              type: integer
              example: 1
            totalCompanies:
              type: integer
              example: 1
            pageSize:
              type: integer
              example: 10

    LocationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            location:
              type: string
              example: "Company location data"

    ReportCreate:
      type: object
      required:
        - request_type
        - companyId
        - date
      properties:
        request_type:
          type: string
          enum: ["weekly", "monthly", "gaap", "csfa", "benchmark"]
          example: "weekly"
        companyId:
          type: integer
          example: 1
        date:
          type: string
          format: date
          example: "2024-01-01"
        files:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: ["transaction", "ar", "ap", "previous_flow_cash"]
                example: "transaction"
              name:
                type: string
                example: "transaction_file.csv"
              content:
                type: string
                format: base64
                example: "base64_encoded_file_content"

    ReportResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        statusCode:
          type: integer
          example: 201
        message:
          type: string
          example: "Report request created and email sent successfully!"
        data:
          type: object
          properties:
            id:
              type: integer
              example: 1
            name:
              type: string
              example: "CashIQ (13 Week)-projection-January 2024"
            status:
              type: string
              enum: ["pending", "processing", "download", "edit", "error"]
              example: "pending"
            request_type:
              type: string
              enum: ["weekly", "monthly", "gaap", "csfa", "benchmark"]
              example: "weekly"
            companyId:
              type: integer
              example: 1
            date_requested:
              type: string
              format: date-time
              example: "2024-01-01T00:00:00Z"
            cashflow:
              type: boolean
              example: true

    ReportListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/ReportResponse'

    TemplateResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Example created successfully"
        body:
          type: object
          properties:
            id:
              type: integer
              example: 1
            name:
              type: string
              example: "Template Name"
            description:
              type: string
              example: "Template description"
            documents:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                  file_name:
                    type: string
                  file_key:
                    type: string
                  file_type:
                    type: string
                  size:
                    type: string

    TemplateListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/TemplateResponse'

    FAQCreate:
      type: object
      required:
        - question
        - answer
      properties:
        question:
          type: string
          example: "What is Valisight?"
        answer:
          type: string
          example: "Valisight is a financial reporting platform."

    FAQUpdate:
      type: object
      properties:
        question:
          type: string
        answer:
          type: string

    FAQResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "FAQ created successfully"
        faqs:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              question:
                type: string
                example: "What is Valisight?"
              answer:
                type: string
                example: "Valisight is a financial reporting platform."
              createdAt:
                type: string
                format: date-time

    FAQListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        faqs:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              question:
                type: string
                example: "What is Valisight?"
              answer:
                type: string
                example: "Valisight is a financial reporting platform."
              createdAt:
                type: string
                format: date-time

tags:
  - name: Authentication
    description: User authentication and authorization endpoints
  - name: Company
    description: Company management endpoints
  - name: Report
    description: Financial report management endpoints
  - name: Template
    description: Template management endpoints
  - name: FAQ
    description: FAQ management endpoints
