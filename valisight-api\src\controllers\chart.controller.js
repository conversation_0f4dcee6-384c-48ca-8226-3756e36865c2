// backend/src/controllers/chart.controller.js
import { saveChartToS3, saveMultipleChartsToS3, generateChartFileName } from '../services/chartStorage.service.js';
import { handleSuccessResponse, handleErrorResponse } from '../utils/responseHandler.js';
import { prisma } from '../db/prisma.js';

/**
 * Fetch company information and generate folder name
 * @param {number} companyId - Company ID from database
 * @returns {Promise<{companyInfo: Object, folderName: string}>}
 */
async function getCompanyFolderInfo(companyId) {
  try {
    const company = await prisma.company.findUnique({
      where: { id: parseInt(companyId) },
      select: { id: true, name: true }
    });

    if (!company) {
      throw new Error(`Company with ID ${companyId} not found`);
    }

    // Generate folder name: "Company Name (Id: 11)"
    const companyName = company.name || 'Unknown Company';
    const folderName = `${companyName} (Id: ${company.id})`;

    return {
      companyInfo: company,
      folderName: folderName
    };
  } catch (error) {
    console.error('Error fetching company info:', error);
    // Fallback folder name if company not found
    return {
      companyInfo: { id: companyId, name: 'Unknown Company' },
      folderName: `Unknown Company (Id: ${companyId})`
    };
  }
}

/**
 * Fetch report information to get the actual date_requested
 * @param {number} reportId - Report request ID from database
 * @returns {Promise<Date|null>} - Returns the date_requested or null if not found
 */
async function getReportDateRequested(reportId) {
  try {
    if (!reportId || isNaN(reportId)) {
      return null; // Return null if reportId is invalid
    }

    const report = await prisma.reportRequest.findUnique({
      where: { id: parseInt(reportId) },
      select: { date_requested: true }
    });

    return report?.date_requested || null;
  } catch (error) {
    console.error('Error fetching report date_requested:', error);
    return null; // Return null on error, will fallback to current date
  }
}

/**
 * Upload a single chart image to S3
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const uploadSingleChart = async (req, res) => {
  try {
    const { chartData, chartUniqueName, companyId, reportId, dateRequested } = req.body;

    // Validate required fields
    if (!chartData || !chartUniqueName || !companyId || !reportId) {
      return handleErrorResponse(
        new Error('Missing required fields: chartData, chartUniqueName, companyId, reportId'),
        res,
        req,
        'Chart Upload'
      );
    }

    // Get the actual date_requested from the database if reportId is valid
    const actualDateRequested = await getReportDateRequested(reportId);

    // Generate unique filename using new naming convention
    const fileName = generateChartFileName(
      chartUniqueName,
      companyId,
      reportId,
      actualDateRequested || dateRequested || new Date()
    );

    // Convert base64 data to buffer if needed
    let imageBuffer;
    if (chartData.startsWith('data:image/png;base64,')) {
      const base64Data = chartData.replace(/^data:image\/png;base64,/, '');
      imageBuffer = Buffer.from(base64Data, 'base64');
    } else {
      return handleErrorResponse(
        new Error('Invalid chart data format. Expected base64 PNG data.'),
        res,
        req,
        'Chart Upload'
      );
    }

    // Upload to S3 using direct buffer upload with company folder
    const s3Url = await uploadBufferToS3(imageBuffer, fileName, companyId);

    // console.log(`Chart uploaded successfully: ${s3Url}`);

    return handleSuccessResponse(res, 'Chart uploaded successfully', {
      url: s3Url,
      fileName: fileName
    });

  } catch (error) {
    console.error('Error uploading chart:', error);
    return handleErrorResponse(error, res, req, 'Chart Upload');
  }
};

/**
 * Upload multiple chart images to S3
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const uploadMultipleCharts = async (req, res) => {
  try {
    const { charts, companyId, reportId, dateRequested } = req.body;

    // Validate required fields
    if (!charts || !Array.isArray(charts) || charts.length === 0) {
      return handleErrorResponse(
        new Error('Missing or invalid charts array'),
        res,
        req,
        'Multiple Chart Upload'
      );
    }

    if (!companyId || !reportId) {
      return handleErrorResponse(
        new Error('Missing required fields: companyId, reportId'),
        res,
        req,
        'Multiple Chart Upload'
      );
    }

    const uploadResults = [];
    const errors = [];

    // Get the actual date_requested from the database if reportId is valid
    const actualDateRequested = await getReportDateRequested(reportId);

    // Process each chart
    for (let i = 0; i < charts.length; i++) {
      const chart = charts[i];
      
      try {
        const { chartData, chartUniqueName, chartName } = chart;

        if (!chartData || !chartUniqueName) {
          errors.push({
            index: i,
            chartName: chartName || `Chart ${i + 1}`,
            error: 'Missing chartData or chartUniqueName'
          });
          continue;
        }

        // Generate unique filename using new naming convention
        const fileName = generateChartFileName(
          chartUniqueName,
          companyId,
          reportId,
          actualDateRequested || dateRequested || new Date()
        );

        // Convert base64 data to buffer
        if (chartData.startsWith('data:image/png;base64,')) {
          const base64Data = chartData.replace(/^data:image\/png;base64,/, '');
          const imageBuffer = Buffer.from(base64Data, 'base64');

          // Upload to S3 with company folder
          const s3Url = await uploadBufferToS3(imageBuffer, fileName, companyId);

          uploadResults.push({
            chartName: chartName || chartUniqueName,
            chartUniqueName,
            url: s3Url,
            fileName: fileName
          });

          // console.log(`Chart ${i + 1}/${charts.length} uploaded: ${chartName || chartType}`);

        } else {
          errors.push({
            index: i,
            chartName: chartName || `Chart ${i + 1}`,
            error: 'Invalid chart data format'
          });
        }

      } catch (chartError) {
        console.error(`❌ Error uploading chart ${i + 1}:`, chartError);
        errors.push({
          index: i,
          chartName: chart.chartName || `Chart ${i + 1}`,
          error: chartError.message
        });
      }
    }

    const response = {
      message: `Processed ${charts.length} charts`,
      successful: uploadResults.length,
      failed: errors.length,
      results: uploadResults
    };

    if (errors.length > 0) {
      response.errors = errors;
    }

    // console.log(`Chart upload summary: ${uploadResults.length} successful, ${errors.length} failed`);

    return handleSuccessResponse(res, response.message, {
      successful: response.successful,
      failed: response.failed,
      results: response.results,
      ...(response.errors && { errors: response.errors })
    });

  } catch (error) {
    console.error('Error uploading multiple charts:', error);
    return handleErrorResponse(error, res, req, 'Multiple Chart Upload');
  }
};

/**
 * Helper function to upload buffer directly to S3 with company folder structure
 * @param {Buffer} buffer - Image buffer
 * @param {string} fileName - File name
 * @param {number} companyId - Company ID for folder organization
 * @returns {Promise<string>} - S3 URL
 */
async function uploadBufferToS3(buffer, fileName, companyId) {
  const AWS = await import('aws-sdk');

  // Configure AWS SDK
  AWS.default.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION
  });

  const s3 = new AWS.default.S3();

  // Get company folder information
  const { companyInfo, folderName } = await getCompanyFolderInfo(companyId);

  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: `charts/${folderName}/${fileName}`,
    Body: buffer,
    ContentType: 'image/png',
    Metadata: {
      'generated-at': new Date().toISOString(),
      'upload-method': 'frontend-direct',
      'company-id': companyId.toString(),
      'company-name': companyInfo.name || ''
    }
  };

  // console.log(`⬆Uploading to S3: ${params.Bucket}/charts/${folderName}/${fileName}`);
  const uploadResult = await s3.upload(params).promise();

  return uploadResult.Location;
}

/**
 * Test chart upload functionality
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const testChartUpload = async (req, res) => {
  try {
    // Create a simple test chart
    const testChartOptions = {
      series: [44, 55, 13, 43, 22],
      chart: {
        type: 'pie',
        height: 400
      },
      labels: ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],
      colors: ['#FF4560', '#00E396', '#FEB019', '#775DD0', '#546E7A']
    };

    const fileName = generateChartFileName('test-pie-chart', 999, 888, new Date());
    const s3Url = await saveChartToS3(testChartOptions, fileName, false, 999);

    return handleSuccessResponse(res, 'Test chart uploaded successfully', {
      url: s3Url,
      fileName: fileName
    });

  } catch (error) {
    console.error('Test chart upload failed:', error);
    return handleErrorResponse(error, res, req, 'Test Chart Upload');
  }
};

export default {
  uploadSingleChart,
  uploadMultipleCharts,
  testChartUpload
};
