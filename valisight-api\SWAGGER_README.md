# Swagger UI Integration

## Overview

The Valisight API now includes Swagger UI for interactive API documentation. This allows developers to explore and test the API endpoints directly from a web interface.

## Accessing the Documentation

### Development Server
- **Swagger UI**: http://localhost:8000/api-docs
- **OpenAPI JSON**: http://localhost:8000/api-docs.json

### Production Server
- **Swagger UI**: https://your-domain.com/api-docs
- **OpenAPI JSON**: https://your-domain.com/api-docs.json

## Features

### 🔍 Interactive Documentation
- Browse all API endpoints organized by tags
- View request/response schemas
- See validation rules and examples

### 🧪 Try It Out
- Test API endpoints directly from the browser
- Send requests with real data
- View actual responses

### 🔐 Authentication
- JWT Bearer token authentication support
- Click the "Authorize" button to add your token
- Token format: `Bearer <your_jwt_token>`

### 📁 File Uploads
- Test file upload endpoints
- Supported formats: JPEG, JPG, PNG, PDF, CSV, XLSX
- Maximum file size: 10MB

## API Endpoints Covered

### Authentication (5 endpoints)
- User registration
- User login
- Password reset functionality

### Company Management (7 endpoints)
- Company CRUD operations
- File uploads
- Location services

### Report Management (10 endpoints)
- Report creation and management
- File uploads (cashflow/non-cashflow)
- Report downloads and sharing

### Template Management (6 endpoints)
- Template creation and management
- File uploads and downloads

### FAQ Management (5 endpoints)
- FAQ CRUD operations
- Bulk operations

## Getting Started

1. **Start the server**:
   ```bash
   npm run dev
   ```

2. **Open Swagger UI**:
   Navigate to http://localhost:8000/api-docs

3. **Authenticate** (if needed):
   - Click the "Authorize" button
   - Enter your JWT token in format: `Bearer <token>`
   - Click "Authorize"

4. **Test endpoints**:
   - Expand any endpoint
   - Click "Try it out"
   - Fill in required parameters
   - Click "Execute"

## Customization

### Swagger Options
The Swagger UI is configured with the following options:
- **Explorer**: Enabled for better navigation
- **Filter**: Enabled for searching endpoints
- **Try It Out**: Enabled for testing
- **Custom CSS**: Removes Swagger branding

### Configuration File
Swagger configuration is in `src/config/swagger.js`:
- Custom styling
- Server URLs
- UI options

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure CORS is properly configured
2. **File Upload Issues**: Check file size and format restrictions
3. **Authentication Errors**: Verify JWT token format and expiration

### Development Tips

1. **Update OpenAPI Spec**: Edit `openapi.yaml` and restart server
2. **Custom Styling**: Modify `swaggerOptions.customCss`
3. **Add Endpoints**: Update both routes and OpenAPI spec

## Integration with Other Tools

### Postman
- Import the OpenAPI spec from `/api-docs.json`
- Generate Postman collections automatically

### Code Generation
- Use the OpenAPI spec to generate client SDKs
- Support for multiple languages and frameworks

### CI/CD
- Validate API changes against the spec
- Generate documentation automatically

## Security Notes

- Swagger UI is only available in development by default
- Consider disabling in production or adding authentication
- JWT tokens are stored in browser localStorage
- Clear tokens when testing is complete
