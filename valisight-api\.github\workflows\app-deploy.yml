name: Update and Deploy on EC2

on:
  push:
    branches:
      - app

jobs:
  deploy-on-ec2:
    name: SSH and Docker Compose Deployment
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout
        uses: actions/checkout@v4

      # Step 2: Execute Remote SSH Commands
      - name: Remote SSH Commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.APP_SERVER_PUB_IP }}
          username: azureuser
          key: ${{ secrets.APP_SERVER_KEY }}
          port: 22
          script: |
            cd fintuition-backend/

            # Stop existing Docker containers
            make down-app
            
            # git checkout
            git checkout app
            
            # Pull the latest changes from the repository
            git pull origin app
            
            # Build with staging configuration
            make build-app

            # Start Docker containers with staging configuration
            make up-app

            # Apply database migrations
            make migrate-deploy-app
            
            # Clean up unused Docker images, containers, and volumes
            docker system prune --force

