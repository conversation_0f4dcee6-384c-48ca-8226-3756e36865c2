import { prisma } from '../db/prisma.js';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../utils/errorHandler.js';

/**
 * Get comprehensive financial data for a company
 * @param {number} userId - Current user ID
 * @param {number} companyId - Company ID
 * @param {Array} accountIds - Array of account IDs to filter (optional)
 * @param {number} year - Year filter (optional)
 * @param {number} month - Month filter (optional)
 */
// export const getComprehensiveFinancialData = async (
//   userId,
//   companyId,
//   accountIds = [],
//   year = null,
//   month = null,
// ) => {
//   try {
//     console.log(
//       'getComprehensiveFinancialData called with:',
//       userId,
//       companyId,
//       accountIds,
//       year,
//       month,
//     );
//     // First, verify user has access to this company
//     const company = await prisma.company.findFirst({
//       where: {
//         id: companyId,

//         // userId: userId,
//       },
//     });

//     if (!company) {
//       throw new ErrorHandler('Company not found or access denied', 404);
//     }

//     // Step 1: Get first 2 account IDs from Profit & Loss table
//     const baseWhere = {
//       userId: userId,
//       ...(company.qboRealmID && { realmId: company.qboRealmID }),
//       ...(year && { year: year }),
//       ...(month && { month: month }),
//     };

//     // Get first 2 accounts from Profit & Loss (ordered by accountId)
//     const profitLossFirst2 = await prisma.profitLossReport.findMany({
//       where: baseWhere,
//       select: { accountId: true },
//       distinct: ['accountId'],
//       orderBy: { accountId: 'asc' },
//       take: 2,
//     });

//     const targetAccountIds =
//       accountIds.length > 0
//         ? accountIds
//         : profitLossFirst2.map((record) => record.accountId);

//     console.log('Target Account IDs (first 2 from P&L):', targetAccountIds);

//     if (targetAccountIds.length === 0) {
//       throw new ErrorHandler('No data found for this company', 404);
//     }

//     // Step 2: Get Chart of Accounts for these account IDs
//     const chartOfAccounts = await prisma.account.findMany({
//       where: {
//         userId: userId,
//         ...(company.qboRealmID && { realmId: company.qboRealmID }),
//         accountId: { in: targetAccountIds },
//         isActiveAccount: true,
//       },
//       select: {
//         id: true,
//         accountId: true,
//         name: true,
//         fullyQualifiedAccountName: true,
//         type: true,
//         accountSubTypeName: true,
//         accountClassification: true,
//         currentAccountBalance: true,
//         currencyCode: true,
//         isSubAccountFlag: true,
//         parentAccountQuickbooksId: true,
//         createdAt: true,
//         modifiedAt: true,
//       },
//       orderBy: [{ type: 'asc' }, { name: 'asc' }],
//     });

//     // Create account lookup map for binding names
//     const accountLookup = {};
//     chartOfAccounts.forEach((account) => {
//       accountLookup[account.accountId] = {
//         name: account.name,
//         fullyQualifiedAccountName: account.fullyQualifiedAccountName,
//         type: account.type,
//         accountSubTypeName: account.accountSubTypeName,
//         accountClassification: account.accountClassification,
//       };
//     });

//     let balanceSheetDataIds = await prisma.balanceSheetReport.findMany({
//       distinct: ['accountId'],
//       orderBy: [{ year: 'desc' }, { month: 'desc' }],
//       take: 2,
//     });
//     balanceSheetDataIds = balanceSheetDataIds.map((record) => record.accountId);
//     console.log('balanceSheetDataIds', balanceSheetDataIds);

//     // Step 3: Get financial data for these 2 accounts with account names
//     const [profitLossData, trialBalanceData, balanceSheetData] =
//       await Promise.all([
//         // Query 1: Profit & Loss Data
//         prisma.$queryRaw`
//         SELECT
//           pl."id",
//           pl."accountId",
//           pl."year",
//           pl."month",
//           pl."amount",
//           pl."currencyCode",
//           pl."createdAt",
//           pl."modifiedAt",
//           a."name",
//           a."fullyQualifiedAccountName",
//           a."type",
//           a."accountClassification"
//         FROM "ProfitLossReport" pl
//         JOIN "Account" a
//           ON pl."accountId" = a."accountId"
//           AND pl."userId" = a."userId"
//           AND pl."realmId" = a."realmId"
//         WHERE
//           pl."accountId" = ANY(${targetAccountIds})
//           AND pl."userId" = ${userId}
//           AND pl."realmId" = ${company.qboRealmID}
//         ORDER BY
//           pl."accountId",
//           pl."year" ASC,
//           pl."month" ASC
//       `,

//         // Query 2: Trial Balance Data
//         prisma.$queryRaw`
//         SELECT
//           t."id",
//           t."accountId",
//           t."year",
//           t."month",
//           t."monthEndDebitAmount",
//           t."monthEndCreditAmount",
//           t."netChangeAmount",
//           t."createdAt",
//           t."modifiedAt",
//           a."name",
//           a."fullyQualifiedAccountName",
//           a."type",
//           a."accountClassification"
//         FROM "TrialBalanceReport" t
//         JOIN "Account" a
//           ON t."accountId" = a."accountId"
//           AND t."userId" = a."userId"
//           AND t."realmId" = a."realmId"
//         WHERE
//           t."accountId" = ANY(${targetAccountIds})
//           AND t."userId" = ${userId}
//           AND t."realmId" = ${company.qboRealmID}
//         ORDER BY
//           t."accountId",
//           t."year" ASC,
//           t."month" ASC
//         LIMIT 24
//       `,

//         // Query 3: Balance Sheet Data
//         prisma.$queryRaw`
//         SELECT
//           b."id",
//           b."accountId",
//           b."year",
//           b."month",
//           b."statementAmount",
//           b."currencyCode",
//           b."createdAt",
//           b."modifiedAt",
//           a."name",
//           a."fullyQualifiedAccountName",
//           a."type",
//           a."accountClassification"
//         FROM "BalanceSheetReport" b
//         JOIN "Account" a
//           ON b."accountId" = a."accountId"
//           AND b."userId" = a."userId"
//           AND b."realmId" = a."realmId"
//         WHERE
//           b."accountId" = ANY(${balanceSheetDataIds})
//           AND b."userId" = ${userId}
//           AND b."realmId" = ${company.qboRealmID}
//         ORDER BY
//           b."accountId",
//           b."year" ASC,
//           b."month" ASC
//         LIMIT 24
//       `,
//       ]);

//     // Step 4: Get full AR and AP data (company-wide)
//     const [arAgingData, apAgingData] = await Promise.all([
//       prisma.accountReceivableAgingSummaryReport.findMany({
//         where: {
//           userId: userId,
//           realmId: company.qboRealmID, // Remove conditional since it's likely always needed
//           ...(year && { year: year }),
//           ...(month && { month: month }),
//         },
//         select: {
//           id: true,
//           year: true,
//           month: true,
//           total: true,
//           createdAt: true,
//           modifiedAt: true,
//         },
//         orderBy: [
//           { year: 'asc' }, // Use lowercase 'asc' for consistency
//           { month: 'asc' },
//         ],
//       }),

//       prisma.accountPayableAgingSummaryReport.findMany({
//         where: {
//           userId: userId,
//           realmId: company.qboRealmID, // Remove conditional since it's likely always needed
//           ...(year && { year: year }),
//           ...(month && { month: month }),
//         },
//         select: {
//           id: true,
//           year: true,
//           month: true,
//           total: true,
//           createdAt: true,
//           modifiedAt: true,
//         },
//         orderBy: [
//           { year: 'asc' }, // Use lowercase 'asc' for consistency
//           { month: 'asc' },
//         ],
//       }),
//     ]);

//     return {
//       success: true,
//       statusCode: 200,
//       data: {
//         company,

//         chartOfAccounts,
//         profitLoss: profitLossData,
//         trialBalance: trialBalanceData,
//         balanceSheet: balanceSheetData,
//         arAging: arAgingData,
//         apAging: apAgingData,
//       },
//     };
//   } catch (error) {
//     console.error('Error in getComprehensiveFinancialData:', error);
//     if (error instanceof ErrorHandler) throw error;
//     throw new ErrorHandler(
//       'Error while fetching comprehensive financial data',
//       500,
//     );
//   }
// };

/**
 * Get financial data for specific account IDs
 * @param {number} userId - Current user ID
 * @param {number} companyId - Company ID
 * @param {Array} accountIds - Array of account IDs
 */
export const getComprehensiveFinancialData = async (
  userId,
  companyId,
  accountIds = [],
  year = null,
  month = null,
) => {
  try {
    const company = await prisma.company.findFirst({
      where: {
        id: companyId,

        // userId: userId,
      },
    });

    if (!company) {
      throw new ErrorHandler('Company not found or access denied', 404);
    }
    const baseWhere = {
      // userId: userId,
      ...(company.qboRealmID && { realmId: company.qboRealmID }),
    };
    console.log('baseWhere', baseWhere);
    const chartOfAccounts = await prisma.account.findMany({
      where: {
        ...baseWhere,

        isActiveAccount: true,
      },
      select: {
        id: true,
        accountId: true,
        name: true,
        fullyQualifiedAccountName: true,
        type: true,
        accountSubTypeName: true,
        accountClassification: true,
        currentAccountBalance: true,
        currencyCode: true,
        isSubAccountFlag: true,
        parentAccountQuickbooksId: true,
        createdAt: true,
        modifiedAt: true,
      },
      orderBy: [{ type: 'asc' }, { name: 'asc' }],
      take: 5,
    });
    const profitLossFirst2 = await prisma.profitLossReport.findMany({
      where: baseWhere,
      select: { accountId: true },
      distinct: ['accountId'],
      orderBy: { accountId: 'asc' },
      take: 2,
    });
    const targetProfitLossAccountIds =
      accountIds.length > 0
        ? accountIds
        : profitLossFirst2.map((record) => record.accountId);

    const profitLossData = await prisma.$queryRaw`
    SELECT
      pl."id",
      pl."accountId",
      pl."year",
      pl."month",
      pl."amount",
      pl."currencyCode",
      pl."createdAt",
      pl."modifiedAt",
      a."name",
      a."fullyQualifiedAccountName",
      a."type",
      a."accountClassification"
    FROM "ProfitLossReport" pl
    JOIN "Account" a
      ON pl."accountId" = a."accountId"
      AND pl."userId" = a."userId"
      AND pl."realmId" = a."realmId"
    WHERE 
      pl."accountId" = ANY(${targetProfitLossAccountIds})
      AND pl."userId" = ${userId}
      AND pl."realmId" = ${company.qboRealmID}
    ORDER BY 
      pl."accountId",
      pl."year" ASC,
      pl."month" ASC
  `;
    let balanceSheetDataIds = await prisma.balanceSheetReport.findMany({
      distinct: ['accountId'],
      orderBy: [{ year: 'desc' }, { month: 'desc' }],
      take: 2,
    });
    balanceSheetDataIds = balanceSheetDataIds.map((record) => record.accountId);

    const balanceSheetData = await prisma.$queryRaw`
    SELECT
      b."id",
      b."accountId",
      b."year",
      b."month",
      b."statementAmount",
      b."currencyCode",
      b."createdAt",
      b."modifiedAt",
      a."name",
      a."fullyQualifiedAccountName",
      a."type",
      a."accountClassification"
    FROM "BalanceSheetReport" b
    JOIN "Account" a
      ON b."accountId" = a."accountId"
      AND b."userId" = a."userId"
      AND b."realmId" = a."realmId"
    WHERE 
      b."accountId" = ANY(${balanceSheetDataIds})
      AND b."userId" = ${userId}
      AND b."realmId" = ${company.qboRealmID}
    ORDER BY 
      b."accountId",
      b."year" ASC,
      b."month" ASC
    LIMIT 24
  `;

    let trialBalanceIds = await prisma.trialBalanceReport.findMany({
      distinct: ['accountId'],
      orderBy: [{ year: 'desc' }, { month: 'desc' }],
      take: 2,
    });
    trialBalanceIds = trialBalanceIds.map((record) => record.accountId);
    console.log('trialBalanceIds', trialBalanceIds);

    const trialBalanceData = await prisma.$queryRaw`
SELECT
  t."id",
  t."accountId",
  t."year",
  t."month",
  t."monthEndDebitAmount",
  t."monthEndCreditAmount",
  t."netChangeAmount",
  t."createdAt",
  t."modifiedAt",
  a."name",
  a."fullyQualifiedAccountName",
  a."type",
  a."accountClassification"
FROM "TrialBalanceReport" t
JOIN "Account" a
  ON t."accountId" = a."accountId"
  AND t."userId" = a."userId"
  AND t."realmId" = a."realmId"
WHERE 
  t."accountId" = ANY(${trialBalanceIds})
  AND t."realmId" = ${company.qboRealmID}
ORDER BY 
  t."accountId",
  t."year" ASC,
  t."month" ASC
LIMIT 24
`;
    const [arAgingData, apAgingData] = await Promise.all([
      prisma.accountReceivableAgingSummaryReport.findMany({
        where: {
          realmId: company.qboRealmID, // Remove conditional since it's likely always needed
          ...(year && { year: year }),
          ...(month && { month: month }),
        },
        select: {
          id: true,
          year: true,
          month: true,
          total: true,
          createdAt: true,
          modifiedAt: true,
        },
        orderBy: [
          { year: 'asc' }, // Use lowercase 'asc' for consistency
          { month: 'asc' },
        ],
      }),

      prisma.accountPayableAgingSummaryReport.findMany({
        where: {
          realmId: company.qboRealmID, // Remove conditional since it's likely always needed
          ...(year && { year: year }),
          ...(month && { month: month }),
        },
        select: {
          id: true,
          year: true,
          month: true,
          total: true,
          createdAt: true,
          modifiedAt: true,
        },
        orderBy: [
          { year: 'asc' }, // Use lowercase 'asc' for consistency
          { month: 'asc' },
        ],
      }),
    ]);
    return {
      success: true,
      statusCode: 200,
      data: {
        company,

        chartOfAccounts,
        profitLoss: profitLossData,
        trialBalance: trialBalanceData,
        balanceSheet: balanceSheetData,
        arAging: arAgingData,
        apAging: apAgingData,
      },
    };
  } catch (error) {
    console.error('Error in getComprehensiveFinancialData:', error);
    if (error instanceof ErrorHandler) throw error;
    throw new ErrorHandler(
      'Error while fetching comprehensive financial data',
      500,
    );
  }
};
