{"name": "fintuiton", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon node src/index.js", "migrate": "npx prisma migrate dev", "start": "node src/index.js", "build": "echo \"No build step required (example).\"", "studio": "npx prisma studio", "migration:generate": "npx prisma migrate dev", "migration:run": "npx prisma migrate deploy"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.717.0", "@aws-sdk/s3-request-presigner": "^3.717.0", "@prisma/client": "^6.10.1", "@sendgrid/mail": "^8.1.4", "archiver": "^7.0.1", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "file-type": "^19.6.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.16", "pdf-lib": "^1.17.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "js-yaml": "^4.1.0"}, "devDependencies": {"aws-sdk": "^2.1692.0", "chai": "^5.2.0", "nodemon": "^3.1.9", "playwright": "^1.54.1", "prettier": "^3.4.2", "prisma": "^6.10.1", "supertest": "^7.0.0"}}