import axiosInstance from "./axiosInstance";

/**
 * Get all content settings for a specific company
 * @param {number} companyId - The company ID
 */
export const getAllContentSettings = async (companyId) => {
  return await axiosInstance.get(`/content-settings/company/${companyId}`);
};

/**
 * Get content settings by report type for a specific company
 * @param {number} companyId - The company ID
 * @param {string} reportType - The report type (e.g., 'deepsight')
 */
export const getContentSettingsByReportType = async (companyId, reportType) => {
  return await axiosInstance.get(`/content-settings/company/${companyId}/${reportType}`);
};

/**
 * Update content settings for a specific report type and company
 * @param {number} companyId - The company ID
 * @param {string} reportType - The report type (e.g., 'deepsight')
 * @param {Object} data - The settings data
 * @param {Object} data.chartSettings - Chart configuration settings
 * @param {string} data.promptDescription - Prompt description text
 */
export const updateContentSettings = async (companyId, reportType, data) => {
  return await axiosInstance.put(`/content-settings/company/${companyId}/${reportType}`, data);
};
