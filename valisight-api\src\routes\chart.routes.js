// backend/src/routes/chart.routes.js
import express from 'express';
import { uploadSingleChart, uploadMultipleCharts, testChartUpload } from '../controllers/chart.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

const router = express.Router();

/**
 * @route POST /api/charts/upload-single
 * @desc Upload a single chart image to S3
 * @access Private
 * @body {
 *   chartData: string (base64 PNG data),
 *   chartUniqueName: string,
 *   companyId: number,
 *   reportId: number,
 *   dateRequested?: string (ISO date)
 * }
 */
router.post('/upload-single', authenticate, uploadSingleChart);

/**
 * @route POST /api/charts/upload-multiple
 * @desc Upload multiple chart images to S3
 * @access Private
 * @body {
 *   charts: Array<{
 *     chartData: string (base64 PNG data),
 *     chartUniqueName: string,
 *     chartName?: string
 *   }>,
 *   companyId: number,
 *   reportId: number,
 *   dateRequested?: string (ISO date)
 * }
 */
router.post('/upload-multiple', authenticate, uploadMultipleCharts);

/**
 * @route POST /api/charts/test
 * @desc Test chart upload functionality
 * @access Private
 */
router.post('/test', authenticate, testChartUpload);

export default router;
