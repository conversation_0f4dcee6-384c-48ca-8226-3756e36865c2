import React, { useEffect, useState } from "react";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { getAllFaqs } from "../../services/faq";
import CircularProgress from "@mui/material/CircularProgress"; 

const FAQs = () => {
  const [openIndex, setOpenIndex] = useState(null);
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getAll = async () => {
      setLoading(true);
      try {
        const result = await getAllFaqs();
        if (result?.data?.success && result.data.faqs.length > 0) {
          setFaqs(result.data.faqs);
        } else {
          setFaqs([]); 
        }
      } catch (error) {
        console.error("Error fetching FAQs:", error);
        setFaqs([]); 
      } finally {
        setLoading(false); 
      }
    };

    getAll();
  }, []);

  return (
    <div className="max-w-3xl mx-auto px-4 font-inter">
      <div className="text-center mb-8">
        <h1 className="text-[30px] font-semibold mb-2 mt-8">FAQs</h1>
        <p className="text-[#2E3A44] text-[16px]">
          Need something cleared up? Here are our most frequently asked
          questions.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center">
          <CircularProgress />
        </div>
      ) : faqs.length === 0 ? (
        <div className="text-center text-gray-600 text-[16px]">
          No FAQs available.
        </div>
      ) : (
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className={`rounded-lg overflow-hidden ${
                openIndex === index ? "bg-[#EFF3F6]" : ""
              }`}
            >
              <button
                className="w-full px-6 py-4 flex items-center gap-4 hover:bg-[#EFF3F6] transition-colors"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <div className="w-6 h-6 flex items-center">
                  {openIndex === index ? (
                    <RemoveCircleOutlineIcon className="text-gray-600" />
                  ) : (
                    <AddCircleOutlineIcon className="text-gray-600" />
                  )}
                </div>
                <span className="text-left text-[18px] font-medium flex-grow text-[#151F27]">
                  {faq.question}
                </span>
              </button>

              {openIndex === index && (
                <div className="px-16 pb-4 transition-all">
                  <p className="text-gray-600 text-[16px]">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FAQs;
