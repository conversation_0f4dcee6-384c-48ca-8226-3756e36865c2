import { Router } from 'express';
import { authenticate } from '../middleware/auth.middleware.js';
import * as templateSettingsController from '../controllers/templateSettings.controller.js';

export const templateSettingRoute = Router();

/**
 * GET /api/template-settings
 * Get template settings for the current user
 * Returns custom settings if they exist, otherwise returns global default
 */
templateSettingRoute.get(
  '/', 
  authenticate, 
  templateSettingsController.getTemplateSettings
);

templateSettingRoute.get(
  '/global', 
  authenticate, 
  templateSettingsController.getGlobalTemplateSettings
);


/**
 * PUT /api/template-settings
 * Update template settings
 * - Admin: Updates global settings (affects all users without custom settings)
 * - Regular user: Creates/updates their personal custom settings
 */
templateSettingRoute.put(
  '/', 
  authenticate, 
  templateSettingsController.updateTemplateSettings
);

