import * as reportService from '../services/report.service.js';
import { prisma } from '../db/prisma.js';
export const registerRequest = async (req, res) => {
  try {
    const payload = req.body;
    if (
      payload?.request_type === '13weeks' &&
      !payload.files.some((file) => file.type === 'transaction') &&
      !payload.files.some((file) => file.type === 'transaction').content
    ) {
      throw new Error('Transaction upload file is required.');
    }

    res.json(await reportService.processReportRequest(req.body, req.user));
  } catch (err) {
    console.log('error in report request ---', err);
    throw new Error('something went wrong in registerRequest');
  }
};

export const getAllReportRequests = async (req, res) => {
  try {
    const companyId = req.params.id;
    res.json(await reportService.reportRequests(parseInt(companyId)));
  } catch (err) {
    throw new Error('something went wrong in getAllReportRequests');
  }
};

export const uploadFile = async (req, res) => {
  try {
    res.json(await reportService.uploadCashflowFile(req));
  } catch (error) {
    console.log('error', error);
    res.status(500).json({ message: 'File upload failed', error });
  }
};

export const nonCashflowUploadFile = async (req, res) => {
  try {
    res.json(await reportService.uploadNonCashflowFile(req));
  } catch (error) {
    console.log('error', error);
    res.status(500).json({ message: 'File upload failed', error });
  }
};

export const downloadFile = async (req, res) => {
  try {
    res.json(await reportService.downloadReports(parseInt(req.params.id)));
  } catch (err) {
    console.log('err', err);
    throw new Error('something went wrong in getAllReportRequests');
  }
};

export const deleteRequest = async (req, res) => {
  try {
    const request_id = parseInt(req.params.id);
    res.json(await reportService.deleteReportRequest(parseInt(request_id)));
  } catch (err) {
    throw new Error('something went wrong in getAllReportRequests');
  }
};

export const saveEditedReportRequest = async (req, res) => {
  try {
    res.json(await reportService.saveEditedReport(req));
  } catch (err) {
    console.log('error in saveEditedReportRequest', err);
    throw new Error('error in saveEditedReportRequest');
  }
};

export const uploadEditedFileForNonCashFlow = async (req, res) => {
  try {
    res.json(await reportService.saveEditedNonCashFlowReport(req));
  } catch (error) {
    console.log('error', error);
    res.status(500).json({ message: 'File upload failed', error });
  }
};

export const generateReportLink = async (req, res) => {
  try {
    const { key } = req.params;
    res.redirect(await reportService.generateReportLink(key));
  } catch (error) {
    console.log('error', error);
    res.status(500).json({ message: 'Error generating report link', error });
  }
};

export const getReportById = async (req, res) => {
  try {
    const { id } = req.params;
    res.json(await reportService.getReportById(id));
  } catch (error) {
    console.log('error', error);
    res.status(500).json({ message: 'Error getting Report By Id' });
  }
};

function getFiscalYearEnd(dateRequested) {
  // Force it into a Date object
  const req = new Date(dateRequested);

  if (isNaN(req.getTime())) {
    throw new Error("Invalid dateRequested: " + dateRequested);
  }

  const year = req.getFullYear();
  const month = req.getMonth(); // 0 = Jan, 7 = Aug

  // Last day of this month
  const fiscalEndDate = new Date(year, month + 1, 0);

  return fiscalEndDate.toISOString();
}


export async function generateReportCalculations(req, res, next) {
  try {
    const companyData = await reportService.getRealmIdFromCompany(
      req.params.id,
    );

    const currentFiscalYearEnd = companyData?.company?.fiscal_year_end;
    // Fetch the report request by ID to get date_requested
    const reportRequestId = req.params.reportRequestId;

    // Get the report request from the database
    const reportRequest = await prisma.ReportRequest.findUnique({
      where: {
        id: parseInt(reportRequestId),
        companyId: parseInt(req.params.id),
      },
      select: { date_requested: true },
    });

    if (!reportRequest) {
      throw new Error(`ReportRequest with ID ${reportRequestId} not found`);
    }

    const dateRequested = reportRequest.date_requested;

    // Requested year + 1 = fiscal year end year
    const requestedYear = dateRequested.getFullYear();
    const fiscalEndYear = requestedYear + 1;
    // Build fiscal year end date: always 30-Sep of that year

    console.log('dateRequested: ', dateRequested);
    const fiscalEndDate =
      // new Date(fiscalEndYear, 8, 30); // month = 8 → September
      getFiscalYearEnd(dateRequested);

    const updatedFiscalYearEnd = fiscalEndDate;

    // Check if the function returns the expected structure
    const realmId = companyData.success ? companyData.realmId : companyData;
    const companyName = companyData.success
      ? companyData.company.name
      : companyData;

    const { success, message, results, errors } =
      await reportService.calculateAllReportMetrics(
        realmId,
        updatedFiscalYearEnd,
      );

    if (!success) {
      return res.status(404).json({ success: false, message });
    }

    results.companyName = companyName;
    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      data: results,
      errors: errors.length > 0 ? errors : null,
      totalQueries: Object.keys(results).length + errors.length,
      successfulQueries: Object.keys(results).length,
      failedQueries: errors.length,
    });
  } catch (error) {
    console.error('Error fetching financial reports:', error);
    next(error);
  }
}

export const saveText = async (req, res) => {
  try {
    let { text } = req.body;
    let requestId = parseInt(req.params.id);

    // if(!text || text.trim().length === 0){
    //     return res.status(400).json({ success: false, message: "Text is required" });
    // }
    res.json(await reportService.saveText(requestId, text));
  } catch (error) {
    console.log(error.message);
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      success: false,
      statusCode: error.statusCode || 500,
      message: error.message,
    });
  }
};
