import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Container,
  Card,
  Typography,
  Button,
  Stack,
  Chip,
  CircularProgress,
  Fade,
  Paper,
  Divider,
  Alert,
  AlertTitle,
  LinearProgress,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  AccountBalance as AccountBalanceIcon,
  Security as SecurityIcon,
  Sync as SyncIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  CloudSync as CloudSyncIcon,
  Speed as SpeedIcon,
  Shield as ShieldIcon,
  Analytics as AnalyticsIcon,
} from "@mui/icons-material";
import { useParams, useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { getOneById } from "../../services/company";
import { connectQBO, disconnectQBO, getQBOStatus } from "../../services/qbo";
import qboButton from "../../assets/C2QB_green_btn_med_default.svg";
import qboButtonHover from "../../assets/C2QB_green_btn_med_hover.svg";
import qboLogo from "../../assets/qbo-logo.png"; // Add QuickBooks logo if available

const QuickBooksConnect = () => {
  const { companyId } = useParams();
  const navigate = useNavigate();

  const [company, setCompany] = useState(null);
  const [qboConnected, setQboConnected] = useState(false);
  const [qboLoading, setQboLoading] = useState(false);
  const [qboStatus, setQboStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [activeStep, setActiveStep] = useState(0);

  const connectionSteps = [
    {
      label: "Authorize Connection",
      description:
        'Click "Connect to QuickBooks" to begin the secure authorization process',
    },
    {
      label: "QuickBooks Login",
      description: "Log in to your QuickBooks Online account",
    },
    {
      label: "Grant Permissions",
      description: "Allow ValiSights to access your QuickBooks data",
    },
    {
      label: "Complete Setup",
      description: "Finalize the connection and start syncing your data",
    },
  ];

  const benefits = [
    {
      icon: <SyncIcon sx={{ color: "primary.main", fontSize: 32 }} />,
      title: "Real-time Data Synchronization",
      description:
        "Automatically sync your financial data in real-time without manual uploads",
    },
    {
      icon: <AssessmentIcon sx={{ color: "success.main", fontSize: 32 }} />,
      title: "Comprehensive Financial Reports",
      description:
        "Access trial balance, profit & loss, balance sheet, and aging reports instantly",
    },
    {
      icon: <SecurityIcon sx={{ color: "warning.main", fontSize: 32 }} />,
      title: "Bank-level Security",
      description:
        "Your data is protected with OAuth 2.0 and enterprise-grade encryption",
    },
    {
      icon: <SpeedIcon sx={{ color: "info.main", fontSize: 32 }} />,
      title: "Faster Analysis",
      description:
        "Eliminate manual data entry and get insights faster than ever before",
    },
    {
      icon: <AnalyticsIcon sx={{ color: "secondary.main", fontSize: 32 }} />,
      title: "Advanced Analytics",
      description:
        "Unlock powerful financial analytics and benchmarking capabilities",
    },
    {
      icon: <CloudSyncIcon sx={{ color: "primary.main", fontSize: 32 }} />,
      title: "Always Up-to-date",
      description:
        "Your financial data stays current automatically across all reports",
    },
  ];

  const fetchCompanyData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getOneById(companyId);

      if (response.data.success) {
        setCompany(response.data.company);
        setQboConnected(
          response.data.company.qboConnectionStatus === "CONNECTED"
        );
        setQboStatus(response.data.company);
      }
    } catch (error) {
      console.error("Error fetching company:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load company information. Please try again.",
        confirmButtonColor: "#033BD7",
      });
    } finally {
      setLoading(false);
    }
  }, [companyId]);

  const fetchQBOStatus = useCallback(async () => {
    try {
      setQboLoading(true);
      const response = await getQBOStatus(companyId);
      if (response.data) {
        setQboConnected(response.data.qboConnectionStatus === "CONNECTED");
        setQboStatus(response.data);
      }
    } catch (error) {
      console.error("Error fetching QBO status:", error);
      setQboConnected(false);
    } finally {
      setQboLoading(false);
    }
  }, [companyId]);

  const handleQBOConnect = async () => {
    try {
      setQboLoading(true);
      setActiveStep(1);

      const response = await connectQBO(companyId);

      if (response.data.success && response.data?.data?.url) {
        // Redirect to QuickBooks authorization
        window.location.href = response.data?.data?.url;
      } else {
        throw new Error("Failed to get authorization URL");
      }
    } catch (error) {
      console.error("Error connecting to QBO:", error);
      setQboLoading(false);
      setActiveStep(0);

      Swal.fire({
        icon: "error",
        title: "Connection Failed",
        text: "Failed to initiate QuickBooks connection. Please try again.",
        confirmButtonColor: "#033BD7",
      });
    }
  };

  const handleQBODisconnect = async () => {
    try {
      const result = await Swal.fire({
        title: "Disconnect ValiSights from QuickBooks?",

        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#ef4444", // Modern red for destructive action
        cancelButtonColor: "#10b981", // Modern green for safe action
        confirmButtonText: "Yes, disconnect",
        cancelButtonText: "Keep connected",
        width: 500,
      });

      if (result.isConfirmed) {
        setQboLoading(true);
        await disconnectQBO(companyId);
        await fetchQBOStatus();

        Swal.fire({
          icon: "success",
          title: "Successfully Disconnected",
          text: "ValiSights has been disconnected from QuickBooks. You can reconnect anytime.",
          confirmButtonColor: "#033BD7",
        });
      }
    } catch (error) {
      console.error("Error disconnecting QBO:", error);
      Swal.fire({
        icon: "error",
        title: "Disconnect Failed",
        text: "Failed to disconnect ValiSights from QuickBooks. Please check your connection and try again.",
        confirmButtonColor: "#033BD7",
      });
    } finally {
      setQboLoading(false);
    }
  };

  useEffect(() => {
    if (companyId) {
      fetchCompanyData();
    }
  }, [companyId, fetchCompanyData]);

  useEffect(() => {
    if (qboLoading) {
      const interval = setInterval(() => {
        setConnectionProgress((prev) => {
          if (prev >= 90) return 90;
          return prev + Math.random() * 10;
        });
      }, 200);
      return () => clearInterval(interval);
    } else {
      setConnectionProgress(qboConnected ? 100 : 0);
    }
  }, [qboLoading, qboConnected]);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="400px"
        >
          <CircularProgress size={40} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Fade in timeout={600}>
        <Box>
          {/* Header */}
          <Box textAlign="center" sx={{ mb: 6 }}>
            <Stack
              direction="row"
              justifyContent="center"
              alignItems="center"
              spacing={3}
              sx={{ mb: 3 }}
            >
              <Box
                component="img"
                src={qboButton}
                alt="QuickBooks"
                sx={{ height: 60, width: "auto" }}
              />
              <Typography variant="h3" fontWeight={700} color="primary.main">
                +
              </Typography>
              <Typography variant="h4" fontWeight={700}>
                ValiSights
              </Typography>
            </Stack>

            <Typography variant="h4" fontWeight={600} gutterBottom>
              Connect {company?.name} to QuickBooks
            </Typography>

            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: "auto" }}
            >
              Streamline your financial analysis with seamless QuickBooks
              integration
            </Typography>
          </Box>

          {/* Connection Status Card */}
          <Card
            sx={{
              p: 4,
              mb: 4,
              border: "2px solid",
              borderColor: qboConnected ? "success.main" : "grey.300",
              borderRadius: 3,
              background: qboConnected
                ? "linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"
                : "linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)",
              textAlign: "center",
            }}
          >
            {qboConnected ? (
              <Stack spacing={3} alignItems="center">
                <CheckCircleIcon sx={{ fontSize: 64, color: "success.main" }} />
                <Typography variant="h5" fontWeight={600} color="success.dark">
                  Successfully Connected!
                </Typography>
                <Alert severity="success" sx={{ maxWidth: 500 }}>
                  <AlertTitle>
                    Connected to {company?.qboCompanyName || "QuickBooks"}
                  </AlertTitle>
                  Your financial data is now synchronized and ready for
                  analysis.
                </Alert>

                <Stack direction="row" spacing={2}>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={fetchQBOStatus}
                    disabled={qboLoading}
                  >
                    Refresh Status
                  </Button>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={handleQBODisconnect}
                    disabled={qboLoading}
                  >
                    Disconnect
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => navigate(`/companies/${companyId}`)}
                  >
                    View Company Dashboard
                  </Button>
                </Stack>
              </Stack>
            ) : (
              <Stack spacing={3} alignItems="center">
                <ErrorIcon sx={{ fontSize: 64, color: "error.main" }} />
                <Typography variant="h5" fontWeight={600}>
                  Ready to Connect
                </Typography>

                {qboLoading && (
                  <Box sx={{ width: "100%", maxWidth: 400 }}>
                    <LinearProgress
                      variant="determinate"
                      value={connectionProgress}
                      sx={{ height: 8, borderRadius: 4, mb: 2 }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Establishing secure connection...
                    </Typography>
                  </Box>
                )}

                <Box sx={{ position: "relative", display: "inline-block" }}>
                  <Box
                    component="img"
                    src={qboButton}
                    alt="Connect to QuickBooks"
                    sx={{
                      cursor: "pointer",
                      height: 60,
                      width: "auto",
                      transition: "opacity 0.2s ease",
                      "&:hover": { opacity: 0 },
                      opacity: qboLoading ? 0.5 : 1,
                      pointerEvents: qboLoading ? "none" : "auto",
                    }}
                    onClick={!qboLoading ? handleQBOConnect : undefined}
                  />
                  <Box
                    component="img"
                    src={qboButtonHover}
                    alt="Connect to QuickBooks"
                    sx={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      height: 60,
                      width: "auto",
                      opacity: 0,
                      transition: "opacity 0.2s ease",
                      "&:hover": { opacity: 1 },
                      pointerEvents: qboLoading ? "none" : "auto",
                    }}
                    onClick={!qboLoading ? handleQBOConnect : undefined}
                  />
                </Box>
              </Stack>
            )}
          </Card>

          {/* Connection Steps */}
          {!qboConnected && (
            <Card sx={{ p: 4, mb: 4 }}>
              <Typography variant="h5" fontWeight={600} gutterBottom>
                How It Works
              </Typography>
              <Stepper activeStep={activeStep} orientation="vertical">
                {connectionSteps.map((step, index) => (
                  <Step key={index}>
                    <StepLabel>{step.label}</StepLabel>
                    <StepContent>
                      <Typography variant="body2" color="text.secondary">
                        {step.description}
                      </Typography>
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </Card>
          )}

          {/* Benefits Grid */}
          <Typography
            variant="h5"
            fontWeight={600}
            textAlign="center"
            sx={{ mb: 4 }}
          >
            Why Connect to QuickBooks?
          </Typography>

          <Grid container spacing={3}>
            {benefits.map((benefit, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <Card
                  sx={{
                    p: 3,
                    height: "100%",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                    "&:hover": {
                      transform: "translateY(-4px)",
                      boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                    },
                  }}
                >
                  <Stack spacing={2} alignItems="center" textAlign="center">
                    {benefit.icon}
                    <Typography variant="h6" fontWeight={600}>
                      {benefit.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {benefit.description}
                    </Typography>
                  </Stack>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Security Notice */}
          <Card sx={{ p: 3, mt: 4, backgroundColor: "grey.50" }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <ShieldIcon sx={{ color: "success.main", fontSize: 32 }} />
              <Box>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Your Data is Secure
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  We use OAuth 2.0 authentication and bank-level encryption to
                  protect your financial data. ValiSights never stores your
                  QuickBooks login credentials.
                </Typography>
              </Box>
            </Stack>
          </Card>
        </Box>
      </Fade>
    </Container>
  );
};

export default QuickBooksConnect;
