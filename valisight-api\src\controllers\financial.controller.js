import * as financialService from '../services/financial.service.js';
import { ErrorHandler } from '../utils/errorHandler.js';
import Joi from 'joi';

// Validation schemas
const comprehensiveDataValidation = Joi.object({
  companyId: Joi.number().integer().positive().required(),
  accountIds: Joi.array().items(Joi.string()).optional(),
  year: Joi.number().integer().min(2000).max(2100).optional(),
  month: Joi.number().integer().min(1).max(12).optional(),
});

const accountSpecificValidation = Joi.object({
  companyId: Joi.number().integer().positive().required(),
  accountIds: Joi.array().items(Joi.string()).min(1).required(),
});

const entityDataValidation = Joi.object({
  companyId: Joi.number().integer().positive().required(),
  accountIds: Joi.array().items(Joi.string()).optional(),
  year: Joi.number().integer().min(2000).max(2100).optional(),
  month: Joi.number().integer().min(1).max(12).optional(),
});

/**
 * Get comprehensive financial data for a company
 * GET /api/financial/comprehensive/:companyId
 * Query params: accountIds[], year, month
 */
export const getComprehensiveFinancialData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { accountIds, year, month } = req.query;

    // Parse accountIds if provided
    let parsedAccountIds = [];
    if (accountIds) {
      parsedAccountIds = Array.isArray(accountIds) ? accountIds : [accountIds];
    }

    // Validate input
    const { error } = comprehensiveDataValidation.validate({
      companyId,
      accountIds: parsedAccountIds,
      year: year ? parseInt(year) : undefined,
      month: month ? parseInt(month) : undefined,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getComprehensiveFinancialData(
      req.user.id,
      companyId,
      parsedAccountIds,
      year ? parseInt(year) : null,
      month ? parseInt(month) : null,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getComprehensiveFinancialData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching financial data',
    });
  }
};

/**
 * Get financial data for specific account IDs
 * POST /api/financial/by-accounts
 * Body: { companyId, accountIds[] }
 */
export const getFinancialDataByAccounts = async (req, res) => {
  try {
    const { companyId, accountIds } = req.body;

    // Validate input
    const { error } = accountSpecificValidation.validate({
      companyId: parseInt(companyId),
      accountIds,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getFinancialDataByAccounts(
      req.user.id,
      parseInt(companyId),
      accountIds,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getFinancialDataByAccounts controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message:
        'Internal server error while fetching account-specific financial data',
    });
  }
};

/**
 * Get financial summary for a company
 * GET /api/financial/summary/:companyId
 */
export const getFinancialSummary = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);

    if (!companyId || isNaN(companyId)) {
      throw new ErrorHandler('Valid company ID is required', 400);
    }

    // Get comprehensive data but return only summary
    const result = await financialService.getComprehensiveFinancialData(
      req.user.id,
      companyId,
    );

    // Return only summary and company info
    res.status(200).json({
      success: true,
      statusCode: 200,
      data: {
        company: result.data.company,
        summary: result.data.summary,
      },
    });
  } catch (error) {
    console.error('Error in getFinancialSummary controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching financial summary',
    });
  }
};

/**
 * Get chart of accounts with recent financial data
 * GET /api/financial/accounts/:companyId
 * Query params: limit (default: 10)
 */
export const getAccountsWithRecentData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const limit = parseInt(req.query.limit) || 10;

    if (!companyId || isNaN(companyId)) {
      throw new ErrorHandler('Valid company ID is required', 400);
    }

    // Get comprehensive data
    const result = await financialService.getComprehensiveFinancialData(
      req.user.id,
      companyId,
    );

    // Take first 'limit' accounts and their associated data
    const limitedAccounts = result.data.chartOfAccounts.slice(0, limit);
    const accountIds = limitedAccounts.map((acc) => acc.accountId);

    // Filter other financial data to only include these accounts
    const filteredTrialBalance = result.data.trialBalance.filter((tb) =>
      accountIds.includes(tb.accountId),
    );
    const filteredProfitLoss = result.data.profitLoss.filter((pl) =>
      accountIds.includes(pl.accountId),
    );
    const filteredBalanceSheet = result.data.balanceSheet.filter((bs) =>
      accountIds.includes(bs.accountId),
    );

    res.status(200).json({
      success: true,
      statusCode: 200,
      data: {
        company: result.data.company,
        summary: {
          ...result.data.summary,
          totalAccounts: limitedAccounts.length,
          totalTrialBalanceRecords: filteredTrialBalance.length,
          totalProfitLossRecords: filteredProfitLoss.length,
          totalBalanceSheetRecords: filteredBalanceSheet.length,
        },
        chartOfAccounts: limitedAccounts,
        trialBalance: filteredTrialBalance,
        profitLoss: filteredProfitLoss,
        balanceSheet: filteredBalanceSheet,
        arAging: result.data.arAging,
        apAging: result.data.apAging,
      },
    });
  } catch (error) {
    console.error('Error in getAccountsWithRecentData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching accounts with recent data',
    });
  }
};

// ============ INDEPENDENT ENTITY CONTROLLERS ============

/**
 * Get Chart of Accounts data independently
 * GET /api/financial/chart-of-accounts/:companyId
 * Query params: accountIds[]
 */
export const getChartOfAccounts = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { accountIds } = req.query;

    // Parse accountIds if provided
    let parsedAccountIds = [];
    if (accountIds) {
      parsedAccountIds = Array.isArray(accountIds) ? accountIds : [accountIds];
    }

    // Validate input
    const { error } = entityDataValidation.validate({
      companyId,
      accountIds: parsedAccountIds,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getChartOfAccounts(
      req.user.id,
      companyId,
      parsedAccountIds,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getChartOfAccounts controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching chart of accounts',
    });
  }
};

/**
 * Get Profit & Loss data independently
 * GET /api/financial/profit-loss/:companyId
 * Query params: accountIds[], year, month
 */
export const getProfitLossData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { accountIds, year, month } = req.query;

    // Parse accountIds if provided
    let parsedAccountIds = [];
    if (accountIds) {
      parsedAccountIds = Array.isArray(accountIds) ? accountIds : [accountIds];
    }

    // Validate input
    const { error } = entityDataValidation.validate({
      companyId,
      accountIds: parsedAccountIds,
      year: year ? parseInt(year) : undefined,
      month: month ? parseInt(month) : undefined,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getProfitLossData(
      req.user.id,
      companyId,
      parsedAccountIds,
      year ? parseInt(year) : null,
      month ? parseInt(month) : null,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getProfitLossData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching profit & loss data',
    });
  }
};

/**
 * Get Trial Balance data independently
 * GET /api/financial/trial-balance/:companyId
 * Query params: accountIds[], year, month
 */
export const getTrialBalanceData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { accountIds, year, month } = req.query;

    // Parse accountIds if provided
    let parsedAccountIds = [];
    if (accountIds) {
      parsedAccountIds = Array.isArray(accountIds) ? accountIds : [accountIds];
    }

    // Validate input
    const { error } = entityDataValidation.validate({
      companyId,
      accountIds: parsedAccountIds,
      year: year ? parseInt(year) : undefined,
      month: month ? parseInt(month) : undefined,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getTrialBalanceData(
      req.user.id,
      companyId,
      parsedAccountIds,
      year ? parseInt(year) : null,
      month ? parseInt(month) : null,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getTrialBalanceData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching trial balance data',
    });
  }
};

/**
 * Get Balance Sheet data independently
 * GET /api/financial/balance-sheet/:companyId
 * Query params: accountIds[], year, month
 */
export const getBalanceSheetData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { accountIds, year, month } = req.query;

    // Parse accountIds if provided
    let parsedAccountIds = [];
    if (accountIds) {
      parsedAccountIds = Array.isArray(accountIds) ? accountIds : [accountIds];
    }

    // Validate input
    const { error } = entityDataValidation.validate({
      companyId,
      accountIds: parsedAccountIds,
      year: year ? parseInt(year) : undefined,
      month: month ? parseInt(month) : undefined,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getBalanceSheetData(
      req.user.id,
      companyId,
      parsedAccountIds,
      year ? parseInt(year) : null,
      month ? parseInt(month) : null,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getBalanceSheetData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching balance sheet data',
    });
  }
};

/**
 * Get AR Aging data independently
 * GET /api/financial/ar-aging/:companyId
 * Query params: year, month
 */
export const getARAgingData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { year, month } = req.query;

    // Validate input
    const { error } = entityDataValidation.validate({
      companyId,
      year: year ? parseInt(year) : undefined,
      month: month ? parseInt(month) : undefined,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getARAgingData(
      req.user.id,
      companyId,
      year ? parseInt(year) : null,
      month ? parseInt(month) : null,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getARAgingData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching AR aging data',
    });
  }
};

/**
 * Get AP Aging data independently
 * GET /api/financial/ap-aging/:companyId
 * Query params: year, month
 */
export const getAPAgingData = async (req, res) => {
  try {
    const companyId = parseInt(req.params.companyId);
    const { year, month } = req.query;

    // Validate input
    const { error } = entityDataValidation.validate({
      companyId,
      year: year ? parseInt(year) : undefined,
      month: month ? parseInt(month) : undefined,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await financialService.getAPAgingData(
      req.user.id,
      companyId,
      year ? parseInt(year) : null,
      month ? parseInt(month) : null,
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in getAPAgingData controller:', error);

    if (error instanceof ErrorHandler) {
      return res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'Internal server error while fetching AP aging data',
    });
  }
};
