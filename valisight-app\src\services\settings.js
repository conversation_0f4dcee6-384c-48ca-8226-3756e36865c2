import axiosInstance from "./axiosInstance";

export const getDefaultUsersList = async (payload) => {
   return await axiosInstance.post(`/settings/default-users`, payload);
};

export const addUserInSharedList = async (payload) => {
   return await axiosInstance.post(`/settings/add-share-with-user`, payload)
}

export const getSharedUsersList = async () => {
   return await axiosInstance.get(`/settings/shared-users`);
};

export const revokeAccess = async (id) => {
   return await axiosInstance.delete(`/settings/shared-users/${id}`)
}