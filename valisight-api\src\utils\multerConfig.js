import multer from 'multer';
import path from 'path';

// Set up multer memory storage
const storage = multer.memoryStorage();

export const upload = multer({
    storage,
    fileFilter: (req, file, cb) => {
        const allowedExtensions = /jpeg|jpg|png|pdf|csv|xlsx/;
        const allowedMimeTypes = [
            "image/jpeg",
            "image/jpg",
            "image/png",
            "application/pdf",
            "text/csv",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ];

        const extname = allowedExtensions.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedMimeTypes.includes(file.mimetype);
        
        if (mimetype && extname) {
            cb(null, true);
        } else {
            cb(new Error('File type not supported.'));
        }
    },
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
})
