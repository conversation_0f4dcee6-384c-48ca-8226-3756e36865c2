import React, { useState, useEffect, useCallback } from "react";
import Swal from "sweetalert2";
import CircularProgress from "@mui/material/CircularProgress";
import { Avatar, Box } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import ReusableTable from "../../../shared-components/Table";
import { getAvatarInitials } from "../../../utils/shared";
import { revokeAccess, getSharedUsersList } from "../../../services/settings";

const SharedCompaniesList = ({ addedUsers, refreshTrigger, onRefresh }) => {
  const [usersList, setUsersList] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [loading, setIsLoading] = useState(false);

  const handleDelete = async (user) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        setIsLoading(true);
        const accessRevokedUser = await revokeAccess(user?.id);
        if (accessRevokedUser?.data?.success) {
          setUsersList((prev) => prev.filter((c) => c.id !== user?.id));
          Swal.fire("Deleted!", "Deleted Successfully.", "success");
        }
      } catch (error) {
        Swal.fire("Error!", "Something went wrong while deleting", "error");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getDefaultSharedUsersList = useCallback(async () => {
    setIsLoading(true);
    try {
      const users = await getSharedUsersList();
      if (users?.data?.success) {
        setUsersList(users?.data?.users);
      }
    } catch (error) {
      console.error("Error fetching shared users list:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    getDefaultSharedUsersList();
  }, [getDefaultSharedUsersList, refreshTrigger]);

  useEffect(() => {
    if (addedUsers.length > 0) {
      setUsersList((prev) => {
        const uniqueUsers = [...prev];

        addedUsers.forEach((user) => {
          if (!uniqueUsers?.some((u) => u.id === user.id)) {
            uniqueUsers.push(user);
          }
        });
        return uniqueUsers;
      });
    }
  }, [addedUsers]);

  const columns = [
    {
      field: "name",
      headerName: "Company Name",
      render: (row) => (
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Avatar sx={{ width: 40, height: 40, marginRight: 2 }}>
            {getAvatarInitials(row?.sharedWithUser?.username)}
          </Avatar>
          {row?.sharedWithUser?.username}
        </Box>
      ),
    },
  ];

  const actions = [
    {
      tooltip: "Delete",
      icon: <DeleteIcon />,
      onClick: handleDelete,
      className: "text-gray-500 hover:text-red-500",
    },
  ];

  return (
    <>
      {loading ? (
        <div className="flex justify-center items-center mt-[10rem]">
          <CircularProgress size={50} />
        </div>
      ) : usersList?.length > 0 ? (
        <ReusableTable
          columns={columns}
          data={usersList}
          page={page}
          rowsPerPage={rowsPerPage}
          count={usersList?.length}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          actions={actions}
        />
      ) : (
        <div className="flex justify-center items-center mt-[10rem] text-2xl">
          No User Added To Share Companies With
        </div>
      )}
    </>
  );
};

export default SharedCompaniesList;
