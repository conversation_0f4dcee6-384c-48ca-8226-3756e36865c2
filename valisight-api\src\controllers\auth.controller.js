import * as authService from '../services/auth.service.js';
import { authValidation } from '../validations/auth.validation.js';

export const register = async (req, res) => {
  try {
    const { username, password, email ,isAdmin} = req.body;
    // add validation
    // add validation
    const { error } = authValidation.register.validate({
      email,
      password,
      username,
      isAdmin
    });
    if (error) {
      throw {
        status: 200,
        success: false,
        statusCode: 400,
        message: error.details[0].message,
      };
    }


    res.json(await authService.register(username, password, email,isAdmin));
  } catch (error) {
    
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
};
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // add validation
    const { error } = authValidation.login.validate({
      email,
      password,
    });
    if (error) {
      throw {
        status: 200,
        success: false,
        statusCode: 400,
        message: error.details[0].message,
      };
    }

    res.json(await authService.login(email, password));
  } catch (error) {
    
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      status: error.status,
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
};


export const forgetPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // add validation
    const { error } = authValidation.forgetPassword.validate({
      email,
    });
    if (error) {
      throw {
        status: 200,
        success: false,
        statusCode: 400,
        message: error.details[0].message,
      };
    }

    res.json(await authService.forgetPassword(email));
  } catch (error) {
    
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      status: error.status,
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
};

export const resetPasswordEmail = async (req, res) => {
  try {
    const { email } = req.body;

    // add validation
    const { error } = authValidation.forgetPassword.validate({ email });
    if (error) {
      throw {
        status: 200,
        success: false,
        statusCode: 400,
        message: error.details[0].message,
      };
    }

    res.json(await authService.sendResetPasswordEmail(email));
  } catch (error) {
    
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      status: error.status,
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
}

export const resetPassword = async (req, res) => {
  try {
    const { password, confirmPassword, isPasswordReset } = req.body;
    const { token } = req.params;

    // add validation
    const { error } = authValidation.resetPassword.validate({
      password,
      confirmPassword,
      token,
    });
    if (error) {
      throw {
        status: 200,
        success: false,
        statusCode: 400,
        message: error.details[0].message,
      };
    }

    res.json(await authService.resetPassword(password, token, isPasswordReset));
  } catch (error) {
    
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      status: error.status,
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
};
