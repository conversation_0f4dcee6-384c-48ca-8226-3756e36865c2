import React, { useState } from "react";
import logo from "../assets/Logo1.png";
import Cookies from "js-cookie";
import { clearAllCookies } from "../utils/cookies";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { getAvatarInitials } from "../utils/shared";

const TopBar = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const userName = Cookies.get("username");

  const handleLogout = () => {
    clearAllCookies();
    toggleDropdown();
    navigate("/login");
  };
  const handleSharedCompanies = () => {
    navigate(`/share-companies`);
    setIsDropdownOpen(!isDropdownOpen);
  };

  // const handleResetPassword = () =>{
  //   navigate(`/reset-link`);

  // }

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const isActive = (path) => location.pathname === path;

  const isCompaniesActive =
    isActive("/companies") ||
    !["/examples", "/faqs"]?.some((path) => isActive(path));

  return (
    <div className="flex justify-between items-center bg-white px-12 py-4 shadow-md">
      <div className="flex items-center">
        <Link to="/dashboard">
          <img src={logo} alt="Company Logo" className="h-10 w-10" />
        </Link>
        <h1 className="text-lg ml-2">
          <Link to="/dashboard">Valisights</Link>
        </h1>
      </div>

      <div className="relative flex items-center justify-center gap-10 mt-3">
        {/* Navigation Links */}
        <div>
          <Link
            to="/companies"
            className={`${
              isCompaniesActive
                ? "font-bold text-blue-500 underline underline-offset-4 decoration-2"
                : "text-[#2E3A44]"
            } hover:text-blue-500`}
          >
            Companies
          </Link>
        </div>
        <div>
          <Link
            to="/examples"
            className={`${
              isActive("/examples")
                ? "font-bold text-blue-500 underline underline-offset-4 decoration-2"
                : "text-[#2E3A44]"
            } hover:text-blue-500`}
          >
            Examples
          </Link>
        </div>
        <div>
          <Link
            to="/faqs"
            className={`${
              isActive("/faqs")
                ? "font-bold text-blue-500 underline underline-offset-4 decoration-2"
                : "text-[#2E3A44]"
            } hover:text-blue-500`}
          >
            FAQs
          </Link>
        </div>

        {/* User Profile Dropdown */}
        <div
          className="h-10 w-10 flex items-center justify-center rounded-full bg-blue-500 text-white font-bold cursor-pointer"
          onClick={toggleDropdown}
        >
          {getAvatarInitials(userName)}
        </div>

        {isDropdownOpen && (
          <div className="absolute z-10 right-0 mt-[140px] w-48 bg-white border rounded-lg shadow-lg">
            <ul className="py-2">
              <li
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={handleSharedCompanies}
              >
                Share Companies
              </li>
              <li
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={handleLogout}
              >
                Logout
              </li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopBar;
