import axiosInstance from "./axiosInstance";

export const getContentSettings = async (companyId, reportType = 'DEEPSIGHT') => {
  return await axiosInstance.get(`/content-settings/company/${companyId}/${reportType}`);
};

export const checkQBOConnectionStatus = async (companyId) => {
  return await axiosInstance.get(`/qbo/status?companyId=${companyId}`);
};

export const getTemplateSettings = async () => {
  return await axiosInstance.get('/template-settings');
};

export const getGlobalSettings = async (reportType = 'DEEPSIGHT') => {
  return await axiosInstance.get(`/template-settings/global?reportType=${reportType}`);
};

export const updateTemplateSettings = async (settings) => {
  const payload = {
    settings: settings
  };
  return await axiosInstance.put('/template-settings', payload);
};

export const generateReportCalculation = async (companyId, reportId) => {
  return await axiosInstance.get(`/report/${companyId}/generate-calculation/${reportId}`);
};

export const uploadChartsToS3 = async (charts, companyId, reportId, dateRequested) => {
  const payload = {
    charts: charts,
    companyId: parseInt(companyId),
    reportId: reportId,
    dateRequested: dateRequested
  };
  return await axiosInstance.post('/charts/upload-multiple', payload);
};
