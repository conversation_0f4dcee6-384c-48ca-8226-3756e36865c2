export const RequestType = Object.freeze({
    MONTHLY: 'monthly',
    GAAP_ANALYZER: 'gaap',
    CSFA: 'csfa',
    BENCHMARK: 'benchmark',
    WEEKLY: 'weekly',
    DEEPSIGHT: 'deepsight'
});

export const GAAPAnalyzerReports = Object.freeze({
    BALANCE_SHEET: 'balance_sheet',
    INCOME_STATEMENT: 'income_statement'
})

export const WeeklyReportTypes = Object.freeze({
    TRANSACTION: 'transaction',
    AR: 'ar',
    AP: 'ap',
    PREVIOUS_CASH_FLOW: 'previous_flow_cash'
})

export const ReportRequestStatus = Object.freeze({
    PENDING: 'pending',
    PROCESSING: 'processing',
    DOWNLOAD: 'download',
    EDIT: 'edit',
    ERROR: 'error'
})