import Jo<PERSON> from "joi";

export const faqsValidation = {
    createFaqs: Joi.array().items(
        Joi.object({
            question: Joi.string().trim().min(1).required().messages({
                "string.base": "Question must be a string.",
                "string.empty": "Question cannot be empty.",
                "any.required": "Question is required"
            }),
            answer: Joi.string().trim().min(1).required().messages({
                "string.base": "Answer must be a string.",
                "string.empty": "Answer cannot be empty.",
                "any.required": "Answer is required"
            })
        })
    ).min(1).required().messages({
        "array.base": "FAQs must be an array.",
        "array.min": "At least one FAQ is required.",
        "any.required": "FAQs data is required."
    })
};
