name: Update and Deploy on EC2

on:
  push:
    branches:
      - main

jobs:
  deploy-on-ec2:
    name: SSH and Docker Compose Deployment
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout
        uses: actions/checkout@v2

      # Step 2: Execute Remote SSH Commands
      - name: Remote SSH Commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_PUB_IP }}
          username: azureuser
          key: ${{ secrets.SERVER_KEY }}
          port: 22
          script: |
            cd fintuition-backend/

            # Stop existing Docker containers
            make down

            # Pull the latest changes from the repository
            git pull origin main
            
            #build
            make build

            # Start Docker containers (builds if necessary)
            make up

            # Apply database migrations
            make migrate-deploy
            
            # Clean up unused Docker images, containers, and volumes
            docker system prune --force