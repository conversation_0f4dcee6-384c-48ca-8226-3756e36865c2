import React, { useState, useEffect } from "react";
import {
    Modal,
    Box,
    TextField,
    Button,
    List,
    ListItem,
    ListItemText,
    MenuItem,
    Select,
    IconButton,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useDebouncedSearch } from '../../../hooks/useDebounceSearch';
const SharedUserSettingsModal = ({
    open,
    onClose,
    data,
    onAddUser,
    fetchUsers,
    isCreatedModal,
    addedUsers
}) => {
    const [search, setSearch] = useState("");
    const [page, setPage] = useState(data?.pagination?.currentPage || 1);
    const [itemsPerPage, setItemsPerPage] = useState(
        data?.pagination?.pageSize || 10
    );
    const [filteredUsers, setFilteredUsers] = useState(data?.users || []);
    const debouncedSearch = useDebouncedSearch(search);

    const navigate = useNavigate();

    useEffect(() => {
        setFilteredUsers(
            data?.users?.filter((u) => !addedUsers?.some((added) => added.id === u.id)) || []
        );
    }, [data, addedUsers]);

    const handlePageChange = (newPage) => {
        if (newPage > 0 && newPage <= (data?.pagination?.totalPages || 1)) {
            setPage(newPage);
            fetchUsers(newPage, itemsPerPage, search);
        }
    };

    const handleItemsPerPageChange = (event) => {
        const newSize = Number(event.target.value);
        setItemsPerPage(newSize);
        fetchUsers(page, newSize, search);
    };

    const handleClose = () => {
        if (isCreatedModal) navigate("/share-companies");
        onClose();
        setItemsPerPage(10);
        setPage(1);
        setSearch("");
    };

    const handleAddUser = (newUser) => {
        onAddUser(newUser); // Notify parent to update its state
    };

    const handleSearchChange = (event) => {
        setSearch(event.target.value);
        setPage(1); // Reset to the first page when a new search is made
    };

    useEffect(() => {
        if (fetchUsers) {
            fetchUsers(page, itemsPerPage, debouncedSearch);
        }

    }, [page, itemsPerPage, debouncedSearch]);

    return (
        <Modal
            open={open}
            onClose={handleClose}
            sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
        >
            <Box
                sx={{
                    width: 508,
                    height: "719px",
                    overflow: "auto",
                    bgcolor: "white",
                    p: 3,
                    borderRadius: 2,
                    boxShadow: 24,
                    position: "relative",
                }}
            >
                <IconButton
                    onClick={handleClose}
                    sx={{ position: "absolute", top: 8, right: 8 }}
                >
                    <Close />
                </IconButton>

                <TextField
                    fullWidth
                    label="Search User"
                    variant="outlined"
                    value={search}
                    onChange={handleSearchChange}
                    sx={{ mb: 2, mt: 3 }}
                />
                <>
                    {filteredUsers?.length > 0 ? (
                        <>
                            <List>
                                {filteredUsers?.map((user) => (
                                    <ListItem
                                        key={user.id}
                                        secondaryAction={
                                            <Button
                                                variant="contained"
                                                onClick={() => onAddUser(user)}
                                                disabled={addedUsers?.some((u) => u.id === user.id)}
                                                sx={{ minWidth: 80 }}
                                            >
                                                {addedUsers?.some((u) => u.id === user.id) ? "Added" : "Add"}
                                            </Button>
                                        }
                                    >
                                        <ListItemText primary={user.username} />
                                    </ListItem>
                                ))}
                            </List>

                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    mt: 2,
                                    alignItems: "center",
                                }}
                            >
                                <Button
                                    disabled={page === 1}
                                    onClick={() => handlePageChange(page - 1)}
                                >
                                    Previous
                                </Button>
                                <Select
                                    value={itemsPerPage}
                                    onChange={handleItemsPerPageChange}
                                >
                                    <MenuItem value={10}>10</MenuItem>
                                    <MenuItem value={20}>20</MenuItem>
                                    <MenuItem value={50}>50</MenuItem>
                                </Select>
                                <Button
                                    disabled={page === data?.pagination?.totalPages}
                                    onClick={() => handlePageChange(page + 1)}
                                >
                                    Next
                                </Button>
                            </Box>
                        </>
                    ) : (
                        <div className="flex justify-center items-center mt-[180px] font-medium text-xl">
                            No user
                        </div>
                    )}
                </>
            </Box>
        </Modal>
    );
};

export default SharedUserSettingsModal;
