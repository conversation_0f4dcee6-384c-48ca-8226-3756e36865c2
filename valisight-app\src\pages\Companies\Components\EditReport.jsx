import React, { useState, useCallback } from "react";
import { <PERSON>, Typography, Button, CircularProgress } from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";
import * as pdfjsLib from "pdfjs-dist/webpack";
import { saveEditedReport, saveEdits } from "../../../services/report";
import Swal from "sweetalert2";
import * as diff from "diff";

const Inline = Quill.import("blots/inline");
class LineThrough extends Inline {
  static create(value) {
    let node = super.create();
    node.style.textDecoration = value ? "line-through" : "none";
    node.style.color = value ? "red" : "inherit";
    return node;
  }
}
LineThrough.blotName = "lineThrough";
LineThrough.tagName = "span";
Quill.register(LineThrough);

const EditReport = ({ report, setReportDetail }) => {
  const [aiSummaryText, setAiSummaryText] = useState(``);
  const [loading, setLoading] = useState(false);
  const [isContentLoading, setIsContentLoading] = useState(true);
  const [content, setContent] = useState("");
  const [hasChanges, setHasChanges] = useState(false);
  const [originalPdfContent, setOriginalPdfContent] = useState("");
  const [initialPdfData, setinitialPdfData] = useState("");
  const pdfUrl =
    report?.documents?.length > 0 ? report.documents[0].file_key : null;

  const handleCopyText = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        Swal.fire({
          toast: true,
          position: "top-end",
          icon: "success",
          title: "Copied to clipboard!",
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
        });
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err);
      });
  };

  React.useEffect(() => {
    const fetchAndParsePDF = async () => {
      setIsContentLoading(true);
      try {
        const response = await fetch(pdfUrl);
        if (!response.ok) throw new Error("Failed to fetch PDF");
        const arrayBuffer = await response.arrayBuffer();
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
        const numPages = pdf.numPages;

        // Section keywords
        const sections = {
          reportSummary: "Report Summary",
          ratioAnalysis: "Ratio Analysis",
          ratioBenchmark: "Ratio Benchmark",
          financialBenchmark: "Financial Statement Benchmark",
          nextHeading: "Current Fiscal Year",
          probabilityRatios: "Proftability Ratios",
          glossary: "Glossary",
        };

        // Patterns for exclusion
        const ratioMetricsPatterns = [
          /^Previous Year$/i,
          /^Last Year$/i,
          /^Year$/i,
          /^Goods$/i,
          /^Other$/i,
          /^Goods$/i,
          /^Expense$/i,
          /^Assets$/i,
          /^Last$/i,
          /^to$/i,
          /^of$/i,
          /^Capitol$/i,
          /^Jan$/i,
          /^Sep$/i,
          /^This$/i,
          /^Accounts$/i,
          /^Receivable$/i,
          /^Payable$/i,
          /^Credit$/i,
          /^Current$/i,
          /^Stock$/i,
          /^Paid$/i,
          /^in$/i,
          /^and$/i,
          /^Operating$/i,
          /^Profit$/i,
          /^Previous$/i,
          /^Jan$/i,
          /^Date$/i,
          /^Sold$/i,
          /^Income$/i,
          /^$/i,
          /^YTD$/i,
          /^Benchmark$/i,
          /^Revenue$/i,
          /^Variance$/i,
          /^Liabilities$/i,
          /^Earinings$/i,
          /^Equity$/i,
          /^Revnue$/i,
        ];

        const junkPatterns = [
          /^\d+\s+$/,
          /^December 2024 \| Proper Site Service\s+/,
          /^Report Summary\s+/,
          /^(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\s?\|?\s?.*/,
        ];

        let currentSection = null;
        let styledHTML = "";
        let isReportSummaryHeading = false;
        let hasInsertedLineBreak = false;
        let reportSummaryCount = 0;
        let sectionHeaderX = 0;
        const processedSections = new Set();

        // Utility functions
        const shouldExcludeLine = (str) => {
          return ratioMetricsPatterns.some((pattern) =>
            pattern.test(str.trim())
          );
        };

        const removeJunkData = (str) => {
          // Handle Report Summary repetition
          if (str.includes(sections.reportSummary)) {
            if (reportSummaryCount > 0) {
              return "";
            }
            reportSummaryCount++;
          }

          // Remove standalone $ and -
          if (str.trim() === "$" || str.trim() === "-") {
            return "";
          }

          // Exclude lines based on ratio metrics patterns
          if (shouldExcludeLine(str)) {
            return "";
          }

          // Prevent removing standalone 4-digit years
          const words = str.trim().split(/\s+/);
          if (words.length === 1 && /^\d+$/.test(words[0])) {
            if (/^\d{4}$/.test(str)) {
              return str;
            }
            return "";
          }

          // Remove junk patterns
          const cleanedStr = junkPatterns.reduce(
            (s, pattern) => s.replace(new RegExp(pattern, "gm"), ""),
            str
          );

          return cleanedStr;
        };

        const isTitleCase = (str) => {
          return /^[A-Z][a-z]*(?:[\s-][A-Z][a-z]*)*$/.test(str);
        };

        const isNumericContent = (str) => {
          const numericPattern = /^[\d%.\s]+$/;
          const numericCount = (str.match(/[\d%.]/g) || []).length;
          return (
            numericPattern.test(str.trim()) || numericCount > str.length * 0.3
          );
        };

        // Enhanced function to determine if a line should be a heading
        const determineHeading = (str, isBoldOrItalic, fontSize) => {
          const headingConditions = [
            str.includes(sections.reportSummary),
            str.includes(sections.ratioAnalysis),
            str.includes(sections.ratioBenchmark),
            str.includes(sections.financialBenchmark),
            str.includes(sections.nextHeading),
            str.includes(sections.probabilityRatios),
            str.includes(sections.glossary),
            isBoldOrItalic,
            isTitleCase(str),
            str.includes(":"),
            fontSize > 12,
          ];

          return headingConditions.some((condition) => condition);
        };

        // Process each page
        for (let pageNum = 1; pageNum <= numPages; pageNum++) {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          // Skip first two pages
          if (pageNum <= 2) continue;

          // Get page dimensions
          const pageWidth = page.getViewport({ scale: 1 }).width;
          const pageMidpoint = pageWidth / 2;

          // First pass - detect section headers
          textContent.items.forEach((item) => {
            const { str, transform } = item;
            const [, , , , x] = transform;

            if (
              str.includes(sections.ratioAnalysis) ||
              str.includes(sections.ratioBenchmark)
            ) {
              sectionHeaderX = x;
            }
          });

          // Second pass - process content
          textContent.items.forEach((item) => {
            let { str, transform, fontName, width, fontStyle } = item;
            const [scaleX, skewX, skewY, scaleY, x, y] = transform;
            const isBoldOrItalic =
              fontStyle === "italic" || fontName.includes("Bold");
            const fontSize = Math.sqrt(scaleX ** 2 + skewY ** 2);
            const color = item.color || "#000";
            if (fontSize < 8) {
              return;
            }
            // Clean the string first
            str = removeJunkData(str);

            if (shouldExcludeLine(str)) {
              return;
            }

            let newSection = null;
            if (str.includes(sections.reportSummary) && y > 300) {
              newSection = "reportSummary";
            } else if (str.includes(sections.ratioAnalysis)) {
              newSection = "ratioAnalysis";
              // sectionHeaderX = x;
            } else if (str.includes(sections.ratioBenchmark)) {
              newSection = "ratioBenchmark";
              // sectionHeaderX = x;
            } else if (str.includes(sections.financialBenchmark)) {
              newSection = "financialBenchmark";
            } else if (str.includes(sections.nextHeading)) {
              newSection = "nextHeading";
            } else if (str.includes(sections.probabilityRatios)) {
              newSection = "probabilityRatios";
            } else if (str.includes(sections.glossary)) {
              newSection = "glossary";
            }

            // Only update section if it's a new section type we haven't processed yet
            if (newSection && newSection !== currentSection) {
              if (!processedSections.has(newSection)) {
                currentSection = newSection;
                processedSections.add(newSection);
              }
            }

            // Skip content if we're in the Financial Statement Benchmark section
            if (
              currentSection === "financialBenchmark" ||
              currentSection == "probabilityRatios" ||
              currentSection == "nextheading" ||
              currentSection == "ratioAnalysis" ||
              currentSection == "ratioBenchmark"
            ) {
              return;
            }

            // Process ratio sections (only right side content)
            if (
              (currentSection === "ratioAnalysis" ||
                currentSection === "ratioBenchmark") &&
              str.trim() &&
              x > sectionHeaderX + 20
            ) {
              // Skip numeric content in ratio sections
              if (isNumericContent(str)) {
                return;
              }

              if (isReportSummaryHeading && !hasInsertedLineBreak) {
                styledHTML += `<br />`;
                hasInsertedLineBreak = true;
              }

              // Only process right half of the page content for ratio sections
              if (x > pageMidpoint) {
                // Determine if this is a heading with enhanced logic
                const isHeading = determineHeading(
                  str,
                  isBoldOrItalic,
                  fontSize
                );

                // Always make headings bold and in a consistent color
                const formattedStr = isHeading
                  ? `<strong style="color: #000; font-weight: bold;">${str}</strong>`
                  : str;

                styledHTML += `
                  <div style="
                    position: absolute;
                    left: ${x}px;
                    top: ${y}px;
                    font-size: ${fontSize}px;
                    font-family: ${fontName};
                    font-style: ${fontStyle || "normal"};
                    font-weight: ${isHeading ? "bold" : "normal"};
                    color: ${isHeading ? "#000" : color};
                    width: ${width}px;
                  ">
                    ${formattedStr}
                  </div>`;
              }
            }
            // Process report summary section (all content)
            else if (
              currentSection === "reportSummary" ||
              currentSection === "glossary"
            ) {
              if (isReportSummaryHeading && str.trim() === "1") {
                isReportSummaryHeading = false;
                return;
              }

              if (str.trim()) {
                // Determine if this is a heading with enhanced logic
                const isHeading = determineHeading(
                  str,
                  isBoldOrItalic,
                  fontSize
                );

                // Always make headings bold and in a consistent color
                const formattedStr = isHeading
                  ? `<br><strong style="color: #000; font-weight: bold;">${str}</strong></br>`
                  : str;

                styledHTML += `
                  <div style="
                    position: absolute;
                    left: ${x}px;
                    top: ${y}px;
                    font-size: ${fontSize}px;
                    font-family: ${fontName};
                    font-style: ${fontStyle || "normal"};
                    font-weight: ${isHeading ? "bold" : "normal"};
                    color: ${isHeading ? "#000" : color};
                    width: ${width}px;
                  ">
                    ${formattedStr}
                  </div>`;
              }
            }
          });
        }
        const cleanedContent = cleanContent(styledHTML);

        if (report.text) {
          setinitialPdfData(cleanedContent);
        } else {
          setContent(cleanedContent);
          setAiSummaryText(cleanedContent);
          setOriginalPdfContent(cleanedContent);
        }
      } catch (error) {
        console.error("Error processing PDF:", error);
      } finally {
        setIsContentLoading(false);
      }
    };
    const cleanContent = (content) => {
      return content.replace(/(^|\s)\$(\s|$)/g, " ");
    };
    const fetchAndParseGAAPPDF = async () => {
      setIsContentLoading(true);
      try {
        const response = await fetch(pdfUrl);
        if (!response.ok) throw new Error("Failed to fetch GAAP PDF");
        const arrayBuffer = await response.arrayBuffer();
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
        const numPages = pdf.numPages;
        let styledHTML = "";
        const textToRemove = "Now Analytics Spark Shift Energy Solutions";
        const nonBoldPhrases = [
          "Spark Shift Energy Solutions",
          "Spark",
          "Shift",
          "Energy",
          "Solutions",
        ];
        let isSummarySection = false;

        for (let pageNum = 2; pageNum <= numPages; pageNum++) {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();
          const viewport = page.getViewport({ scale: 1.0 });

          // Group text items by line (using y-coordinate)
          const lines = {};
          textContent.items.forEach((item) => {
            const y = Math.round(item.transform[5]);
            if (!lines[y]) lines[y] = [];
            lines[y].push(item);
          });

          // Sort lines from top to bottom
          const sortedLines = Object.keys(lines)
            .sort((a, b) => b - a) // PDF y-coordinates are inverted (top is higher)
            .map((y) => lines[y]);

          // Process each line
          for (const lineItems of sortedLines) {
            // Sort items in the line from left to right
            lineItems.sort((a, b) => a.transform[4] - b.transform[4]);

            // Combine text for the entire line
            const fullLineText = lineItems
              .map((item) => item.str)
              .join("")
              .trim();

            // Skip lines containing text to remove
            if (fullLineText.includes(textToRemove)) continue;

            // Check for summary section
            if (fullLineText.toLowerCase().includes("summary")) {
              isSummarySection = true;
            }

            if (isSummarySection) {
              const processedLine = processLine(
                lineItems,
                nonBoldPhrases,
                viewport.height
              );
              styledHTML += processedLine;
            }
          }
        }

        const cleanedContent = cleanContent(styledHTML);

        if (report.text) {
          setinitialPdfData(cleanedContent);
        } else {
          setContent(cleanedContent);
          setAiSummaryText(cleanedContent);
          setOriginalPdfContent(cleanedContent);
        }
      } catch (error) {
        console.error("Error processing GAAP PDF:", error);
      } finally {
        setIsContentLoading(false);
      }
    };

    const processLine = (lineItems, nonBoldPhrases, pageHeight) => {
      // Combine text items (already sorted left to right)
      let combinedText = lineItems
        .map((item) => item.str)
        .join("")
        .trim();

      // Clean up junk characters
      const junkPatterns = [
        /^•\s*/,
        /^\*\s*/,
        /^-\s*/,
        /^[\u2022\u25E6\u25CB]\s*/,
        /^o\s*/,
      ];

      junkPatterns.forEach((pattern) => {
        combinedText = combinedText.replace(pattern, "");
      });

      // Get first item's properties for styling
      const firstItem = lineItems[0];
      const x = firstItem.transform[4];
      const y = pageHeight - firstItem.transform[5]; // Convert PDF y-coordinate to normal coordinate
      const shouldBeBold = isHeadingText(
        combinedText,
        firstItem.fontStyle,
        firstItem.fontName,
        nonBoldPhrases
      );

      // Handle lines with colons differently
      if (combinedText.includes(":")) {
        const [beforeColon, afterColon] = combinedText.split(":", 2);
        const indentAmount = 20;
        const lineSpacing = 20;

        return `
          <div style="position: absolute; left: ${x}px; top: ${y}px; font-weight: bold; color: blue;">
            ${beforeColon}:
          </div>
          ${
            afterColon
              ? `
          <div style="position: absolute; left: ${x + indentAmount}px; top: ${
                  y + lineSpacing
                }px; color: blue;">
            ${afterColon.trim()}
          </div>
          `
              : ""
          }
        `;
      }

      // Regular line
      return `
        <div style="position: absolute; left: ${x}px; top: ${y}px; ${
        shouldBeBold ? "font-weight: bold;" : ""
      } color: blue;">
          ${combinedText}
        </div>
      `;
    };

    // Rest of your helper functions remain the same
    // Helper function to determine if text should be bold
    const isHeadingText = (text, fontStyle, fontName, nonBoldPhrases) => {
      if (nonBoldPhrases.some((phrase) => text.includes(phrase))) {
        return false;
      }

      return (
        /^\d+\.\s*[A-Za-z].*/.test(text) || // Numbered headings
        fontStyle === "italic" ||
        fontName.includes("Bold") ||
        /^[A-Z][a-z]*(?:\s[A-Z][a-z]*)*$/.test(text) || // All capitalized words
        text.includes(":") || // Lines with colons
        /^(?:Observation|GAAP Reference|Recommendation|Industry Reference)/.test(
          text
        ) // Specific heading types
      );
    };
    if (report?.text) {
      setContent(report.text);
      setAiSummaryText(report.text);
      setIsContentLoading(false);
      setOriginalPdfContent(report.text);
      fetchAndParsePDF();
      fetchAndParseGAAPPDF();
    } else {
      if (pdfUrl) {
        if (report?.request_type?.includes("gaap")) {
          fetchAndParseGAAPPDF();
        } else {
          fetchAndParsePDF();
        }
      }
    }
  }, [pdfUrl, report?.text]);

  function convertDivToParagraphs(html) {
    // Create a temporary container
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;

    // Extract all divs and replace them with <p> elements
    let output = "";
    tempDiv.querySelectorAll("div").forEach((div) => {
      let textContent = div.innerText.trim();

      if (textContent) {
        // If bold text exists, wrap it in <strong>
        if (div.innerHTML.includes("<strong>")) {
          output += `<p><strong>${textContent}</strong></p>`;
        } else {
          output += `<p>${textContent}</p>`;
        }
      }
    });

    // Replace <br /> with proper paragraph breaks
    output = output.replace(/<br\s*\/?>/gi, "<p><br></p>");

    return output;
  }

  function highlightRemovedContent(original, updated) {
    // Use DOMParser to parse the original and updated HTML content
    const originalDOM = new DOMParser().parseFromString(original, "text/html");
    const updatedDOM = new DOMParser().parseFromString(updated, "text/html");

    // Function to extract text with tags, preserving structure but skipping <br> tags
    function extractContentWithTags(node) {
      let result = "";

      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();

        // Skip <br> tags in the diff process
        if (tagName === "br") {
          result += "<br>"; // Just return the <br> tag as is
        } else {
          // For other elements, open and close the tags
          if (["p", "strong"].includes(tagName)) {
            result += `<${tagName}>`;
          }

          Array.from(node.childNodes).forEach((child) => {
            result += extractContentWithTags(child);
          });

          if (["p", "strong"].includes(tagName)) {
            result += `</${tagName}>`;
          }
        }
      }

      return result;
    }

    // Function to handle diffing
    function diffWithoutBrTags(originalText, updatedText) {
      const originalNoBr = originalText.replace(/<br\s*\/?>/g, "<<br>>"); // Replace <br> with placeholder
      const updatedNoBr = updatedText.replace(/<br\s*\/?>/g, "<<br>>"); // Same for updated text

      // Run the diff on the text content
      const differences = diff.diffWords(originalNoBr, updatedNoBr);

      let result = "";
      differences.forEach((part) => {
        if (part.removed) {
          setHasChanges(true);
          result += `<span style="background-color: yellow; text-decoration: line-through;">${part.value}</span>`;
        } else if (part.added) {
          setHasChanges(true);
          result += `<span style="background-color: yellow;">${part.value}</span>`;
        } else {
          result += part.value;
        }
      });

      return result;
    }

    const originalText = extractContentWithTags(originalDOM.body);
    const updatedText = extractContentWithTags(updatedDOM.body);

    // Run diffing and highlight processing
    let highlightedContent = diffWithoutBrTags(originalText, updatedText);

    return highlightedContent
      .replace(/<<br>>/g, "<br>")
      .replace(/<<\s*/g, "<")
      .replace(/\s*>>/g, ">")
      .replace(/<\/</g, "<")
      .replace(/>strong>/g, "></strong>")
      .replace(/>p>/g, ">")
      .replace(/>\/p>/g, ">")
      .replace(/<\/span>\s*<\/strong>/g, "</span><strong>")
      .replace(/>br/g, "")
      .replace(/\/strong</g, "<");
  }

  React.useEffect(() => {
    // Function to properly extract text only from HTML string
    const extractTextContent = (htmlString) => {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlString;
      // Get text content (removes all HTML)
      let text = tempDiv.textContent || tempDiv.innerText || "";
      // Normalize whitespace (replace multiple spaces, newlines, etc. with a single space)
      text = text.replace(/\s+/g, " ").trim();
      return text;
    };

    const originalText = extractTextContent(aiSummaryText);
    const updatedText = extractTextContent(content);

    setHasChanges(originalText !== updatedText);
  }, [content, aiSummaryText]);

  const handleSave = useCallback(async () => {
    const extractTextContent = (htmlString) => {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlString;
      let text = tempDiv.textContent || tempDiv.innerText || "";
      text = text.replace(/\s+/g, " ").trim();
      return text;
    };
    const originalText = extractTextContent(aiSummaryText);
    const updatedText = extractTextContent(content);

    if (originalText === updatedText) {
      return;
    }

    setLoading(true);
    const original = convertDivToParagraphs(aiSummaryText);
    const updatedContent = highlightRemovedContent(original, content);

    try {
      const payload = {
        text: updatedContent,
      };

      const response = await saveEdits(report?.id, payload);
    } catch (err) {
      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: "Failed to send email. Please try again.",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });
    } finally {
      setLoading(false);
    }
  }, [aiSummaryText, content, report?.id]);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (hasChanges) {
        handleSave();
      }
    }, 4000);

    return () => clearTimeout(timer);
  }, [content, hasChanges, handleSave]);

  const handleSubmit = async () => {
    const extractTextContent = (htmlString) => {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlString;
      let text = tempDiv.textContent || tempDiv.innerText || "";
      text = text.replace(/\s+/g, " ").trim();
      return text;
    };

    const originalText = extractTextContent(originalPdfContent);
    const updatedText = extractTextContent(content);

    const hasRealChanges = originalText !== updatedText;

    if (!hasRealChanges) {
    }

    const original = convertDivToParagraphs(initialPdfData);
    const updatedContent = highlightRemovedContent(original, content);

    setLoading(true);
    try {
      const payload = {
        updated_text: hasChanges ? updatedContent : null,
        companyId: report?.companyId,
        file_name:
          report?.documents?.length > 0 ? report?.documents[0].file_name : "",
      };

      const response = await saveEditedReport(report?.id, payload);
      if (response?.data?.request && response.data?.statusCode === 200) {
        Swal.fire({
          toast: true,
          position: "top-end",
          icon: "success",
          title: hasChanges
            ? "Report Edited, Check your email!"
            : "Report edited successfully!",
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
        });
        setReportDetail(null);
      }
    } catch (err) {
      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: "Failed to send email. Please try again.",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });
    } finally {
      setLoading(false); // Set loading to false after API call completes
    }
  };

  return (
    <Box sx={{ width: "100%", height: "100%" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
          px: { xs: 2, md: 4 },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Edit Report
        </Typography>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            bgcolor: "blue",
            color: "white",
            "&:hover": { bgcolor: "blue" },
          }}
          disabled={loading} // Disable button if loading
        >
          {loading ? (
            <CircularProgress size={20} sx={{ color: "white" }} />
          ) : (
            "Submit"
          )}{" "}
          {/* Show loader or text */}
        </Button>
      </Box>

      {/* Main Content */}

      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", lg: "row" },
          gap: 4,
          px: { xs: 2, md: 4 },

          alignItems: "stretch",
        }}
      >
        {/* AI Summary Section */}
        <Box
          sx={{
            flex: 1,
            border: "1px solid #EFF3F6",
            borderRadius: 1,
            bgcolor: "white",
            display: "flex",
            flexDirection: "column",
            maxHeight: "900px",
            position: "relative", // Added for loader positioning
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 3,
              borderBottom: "1px solid #EFF3F6",
              position: "sticky",
              top: 0,
              bgcolor: "white",
              zIndex: 1,
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              AI Summary Text
            </Typography>
            <Button
              variant="outlined"
              onClick={() => handleCopyText(aiSummaryText)}
              startIcon={<ContentCopyIcon />}
              sx={{
                color: "#344054",
                borderColor: "#D9DDE0",
                "&:hover": { borderColor: "#344054" },
              }}
              disabled={isContentLoading}
            >
              Copy
            </Button>
          </Box>
          <Box sx={{ flex: 1, overflowY: "auto", position: "relative" }}>
            {isContentLoading ? (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                  zIndex: 2,
                }}
              >
                <CircularProgress />
              </Box>
            ) : null}
            <>
              <ReactQuill
                className=".ql-editor"
                value={content}
                dangerouslySetInnerHTML={{ __html: content }}
                onChange={(value) => setContent(value)}
                theme="snow"
                modules={{
                  toolbar: [
                    [{ header: [1, 2, 3, false] }],
                    ["bold", "italic", "underline"],
                    [{ list: "ordered" }, { list: "bullet" }],
                    [{ color: ["#000000", "#e60000", "#008a00", "#0066cc"] }], // Add color options
                    ["link"],
                    ["clean"],
                  ],
                }}
                formats={[
                  "header",
                  "bold",
                  "italic",
                  "underline",
                  "strike",
                  "list",
                  "bullet",
                  "link",
                  "image",
                  "color",
                  "lineThrough", // Add custom format
                ]}
              />
            </>
          </Box>
        </Box>
        {/* {content && <>Coming here....<TextRenderer textData={content} /></>} */}
        {/* PDF Report Section */}
        <Box
          sx={{
            flex: 1,
            bgcolor: "#F9FAFB",
            borderRadius: 1,
            display: "flex",
            flexDirection: "column",
            maxHeight: "900px",
            position: "relative", // Added for loader positioning
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "20px 30px",
              borderBottom: "1px solid #EFF3F6",
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              PDF of Monthly Report
            </Typography>
            <Button
              variant="outlined"
              onClick={() => handleCopyText(pdfUrl)}
              startIcon={<ContentCopyIcon />}
              sx={{
                bgcolor: "white",
                color: "#344054",
                borderColor: "#D9DDE0",
                "&:hover": { borderColor: "#344054" },
              }}
              disabled={isContentLoading}
            >
              Copy
            </Button>
          </Box>
          <Box
            sx={{
              flex: 1,
              bgcolor: "white",
              border: "1px solid #EFF3F6",
              borderRadius: 1,
              overflowY: "auto",
              position: "relative",
            }}
          >
            {isContentLoading ? (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                  zIndex: 2,
                }}
              >
                <CircularProgress />
              </Box>
            ) : null}
            <iframe
              src={
                report?.documents?.length > 0
                  ? report.documents[0].file_key
                  : ""
              }
              title="PDF Viewer"
              width="100%"
              height="100%"
              style={{
                border: "none",
              }}
            />
          </Box>
        </Box>
      </Box>
      {/* Summary Section */}
    </Box>
  );
};

export default EditReport;
