import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
  TableContainer,
  Box,
  Tooltip,
  Paper,
  Table,
  Avatar,
  TableBody,
  TableCell,
  TableRow,
  TableFooter,
  TablePagination,
  IconButton,
  TableHead,
  TableSortLabel,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import LastPageIcon from "@mui/icons-material/LastPage";
import FirstPageIcon from "@mui/icons-material/FirstPage";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import KeyboardArrowLeft from "@mui/icons-material/KeyboardArrowLeft";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { getAll, deleteOne } from "../../services/company";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import NoCompanyFound from "../../shared-components/NoDataFound";
import EditCompany from "./Components/Edit";
import CircularProgress from "@mui/material/CircularProgress";
import { formatDate, getAvatarInitials } from "../../utils/shared";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
function TablePaginationActions(props) {
  const theme = useTheme();
  const { count, page, rowsPerPage, onPageChange } = props;

  const handleFirstPageButtonClick = (event) => {
    onPageChange(event, 0);
  };

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  const handleLastPageButtonClick = (event) => {
    onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) - 1));
  };

  return (
    <Box sx={{ flexShrink: 0, ml: 2.5 }}>
      <IconButton
        onClick={handleFirstPageButtonClick}
        disabled={page === 0}
        aria-label="first page"
      >
        {theme.direction === "rtl" ? <LastPageIcon /> : <FirstPageIcon />}
      </IconButton>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 0}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
      <IconButton
        onClick={handleLastPageButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="last page"
      >
        {theme.direction === "rtl" ? <FirstPageIcon /> : <LastPageIcon />}
      </IconButton>
    </Box>
  );
}

TablePaginationActions.propTypes = {
  count: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  rowsPerPage: PropTypes.number.isRequired,
};

const CompaniesList = ({ refresh, searchTerm, setRefreshList }) => {
  const navigate = useNavigate();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [companies, setCompanies] = useState([]);
  const [totalCompanies, setTotalCompanies] = useState(0);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sortConfig, setSortConfig] = useState({ key: "", direction: "asc" });

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const params = {
        page: searchTerm ? 1 : page + 1, // Backend expects 1-based indexing
        pageSize: rowsPerPage,
        search: searchTerm,
        sortBy: sortConfig.key || "",
        sortOrder: sortConfig.direction || "asc",
      };
      const response = await getAll(params);
      const { companies, pagination } = response?.data;
      setCompanies(companies);
      setTotalCompanies(pagination?.totalCompanies);
    } catch (error) {
      console.error("Error fetching companies:", error);
      Swal.fire("Error!", "Failed to fetch companies.", "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, [page, rowsPerPage, refresh, searchTerm, sortConfig]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0); // Reset to first page when changing rows per page
  };

  const handleDelete = async (companyId) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        const response = await deleteOne(companyId);
        if (response.status === 200) {
          Swal.fire("Deleted!", "The company has been deleted.", "success");
          setSelectedCompany(null);
          // Refresh the current page
          fetchCompanies();
        }
      } catch (error) {
        console.error("Error deleting company:", error);
        Swal.fire(
          "Error!",
          "Something went wrong while deleting the company.",
          "error"
        );
      }
    }
  };
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    // Update sort configuration to trigger API call
    setSortConfig({ key, direction });
  };

  const handleEdit = (companyId) => {
    const company = companies.find((c) => c.id === companyId);
    if (company) {
      setSelectedCompany(company);
      setIsEditModalOpen(true);
    }
  };

  const handleSaveCompany = (updatedCompany) => {
    setIsEditModalOpen(false);
    setSelectedCompany(null);
    // Refresh the companies list
    fetchCompanies();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center mt-[18rem]">
        <CircularProgress size={70} sx={{ width: 80, height: 80 }} />
      </div>
    );
  }

  return (
    <div className="p-8 ml-2">
      <div className="flex justify-between items-center mb-6">
        {companies?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 400 }} aria-label="custom pagination table">
              <TableHead>
                <TableRow>
                  {[
                    { label: "Company", key: "companyName" },
                    { label: "Owner", key: "owner" },
                    { label: "Last Request", key: "lastRequest" },
                    { label: "Status", key: "status" },
                  ].map(({ label, key }) => (
                    <TableCell key={key} sx={{ flexGrow: 1 }}>
                      <TableSortLabel
                        active={sortConfig.key === key}
                        direction={
                          sortConfig.key === key ? sortConfig.direction : "asc"
                        }
                        onClick={() => handleSort(key)}
                      >
                        <span className="text-lg font-bold">
                          {label}
                          {/* <IconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSort(key);
                            }}
                          >
                            <FilterAltIcon />
                          </IconButton> */}
                        </span>
                      </TableSortLabel>
                    </TableCell>
                  ))}
                  <TableCell sx={{ flexGrow: 1, textAlign: "center" }}>
                    <span className="text-lg font-bold">Actions</span>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {companies.map((row) => (
                  <TableRow key={row.id} className="cursor-pointer">
                    {/* Company Name */}
                    <TableCell
                      sx={{ flexGrow: 1 }}
                      onClick={() => navigate(`/company/${row.id}`)}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            mr: 2,
                            borderRadius: "50%",
                          }}
                          alt={`${row.name} logo`}
                          src={row.logo}
                        >
                          {!row.logo && getAvatarInitials(row.name)}
                        </Avatar>
                        <span className="text-lg ">
                          {row.name}
                        </span>
                      </Box>
                    </TableCell>
                    {/* Owner Name */}
                    <TableCell
                      sx={{ flexGrow: 1 }}
                      onClick={() => navigate(`/company/${row.id}`)}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <span className="text-lg ">
                          {row?.owner}
                        </span>
                      </Box>
                    </TableCell>
                    {/* Last Request */}
                    <TableCell
                      sx={{ flexGrow: 1 }}
                      onClick={() => navigate(`/company/${row.id}`)}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <span className="text-lg ">
                          {row?.lastRequest
                            ? formatDate(row?.lastRequest)
                            : "N/A"}
                        </span>
                      </Box>
                    </TableCell>
                    {/* Status */}
                    <TableCell
                      sx={{ flexGrow: 1 }}
                      onClick={() => navigate(`/company/${row.id}`)}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <span className="text-lg">
                          {row?.status}
                        </span>
                      </Box>
                    </TableCell>
                    {/* Actions */}
                    <TableCell sx={{ flexGrow: 1, textAlign: "center" }}>
                      <Tooltip title="Delete">
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(row.id);
                          }}
                          aria-label="delete"
                          className="text-gray-500 hover:text-red-500"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(row.id);
                          }}
                          aria-label="edit"
                          className="text-gray-500 hover:text-blue-500"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter>
                <TableRow>
                  <TablePagination
                    rowsPerPageOptions={[5, 10, 25]}
                    colSpan={5}
                    count={totalCompanies}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    SelectProps={{
                      inputProps: { "aria-label": "rows per page" },
                      native: true,
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    ActionsComponent={TablePaginationActions}
                  />
                </TableRow>
              </TableFooter>
            </Table>
          </TableContainer>
        ) : (
          <NoCompanyFound setRefreshList={setRefreshList} />
        )}
        <EditCompany
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          companyData={selectedCompany}
          onSave={handleSaveCompany}
        />
      </div>
    </div>
  );
};

export default CompaniesList;
