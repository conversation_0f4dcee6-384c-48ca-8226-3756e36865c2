import swaggerUi from 'swagger-ui-express';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import yaml from 'js-yaml';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Read and parse the OpenAPI specification file
const openApiSpec = readFileSync(join(__dirname, '../../openapi.yaml'), 'utf8');
const swaggerSpec = yaml.load(openApiSpec);

// Update the server URL to match your Express app
swaggerSpec.servers = [
  {
    url: 'http://localhost:8000/api/v1',
    description: 'Development server'
  }
];

// Swagger UI options
const swaggerOptions = {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Valisight API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    docExpansion: 'list',
    filter: true,
    showRequestHeaders: true,
    tryItOutEnabled: true,
    displayRequestDuration: true,
    defaultModelsExpandDepth: 2,
    defaultModelExpandDepth: 2
  }
};

export { swaggerUi, swaggerSpec, swaggerOptions };
