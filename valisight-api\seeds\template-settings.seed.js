import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Default template settings
const DEFAULT_TEMPLATE_SETTINGS = {
  header: {
    fontStyle: "Helvetica",
    fontType: "Bold",
    fontSize: 44,
    color: "#1e7c8c"
  },
  heading: {
    fontStyle: "Helvetica",
    fontType: "Bold",
    fontSize: 36,
    color: "#1e7c8c"
  },
  subHeading: {
    fontStyle: "Helvetica",
    fontType: "Bold",
    fontSize: 22,
    color: "#1e7c8c"
  },
  content: {
    fontStyle: "Helvetica",
    fontType: "Regular",
    fontSize: 15,
    color: "#333333"
  }
};

/**
 * Seed global default template settings
 */
async function seedTemplateSettings() {
  try {
    console.log('🌱 Seeding template settings...');

    // Check if global default settings already exist
    const existingGlobalSettings = await prisma.templateSettings.findFirst({
      where: {
        userId: null,
        reportType: 'DEEPSIGHT',
        templateType: 'GLOBAL'
      }
    });

    if (existingGlobalSettings) {
      console.log('✅ Global default template settings already exist, skipping seed.');
      return;
    }

    // Create global default template settings
    // Option 1: Let Prisma handle timestamps automatically
    const globalSettings = await prisma.templateSettings.create({
      data: {
        userId: null, // Global settings have no specific user
        reportType: 'DEEPSIGHT',
        templateType: 'GLOBAL',
        settings: DEFAULT_TEMPLATE_SETTINGS,
        updatedBy: null // Initial seed, no specific user updated it
        // Don't specify createdAt and updatedAt - let Prisma handle them
      }
    });

    console.log('✅ Global default template settings created successfully:');
    console.log('   - ID:', globalSettings.id);
    console.log('   - Report Type:', globalSettings.reportType);
    console.log('   - Template Type:', globalSettings.templateType);
    console.log('   - Created At:', globalSettings.createdAt);

  } catch (error) {
    console.error('❌ Error seeding template settings:', error);
    throw error;
  }
}

/**
 * Alternative method if the above doesn't work
 */
async function seedTemplateSettingsAlternative() {
  try {
    console.log('🌱 Seeding template settings (Alternative method)...');

    // Check if global default settings already exist
    const existingGlobalSettings = await prisma.templateSettings.findFirst({
      where: {
        userId: null,
        reportType: 'DEEPSIGHT',
        templateType: 'GLOBAL'
      }
    });

    if (existingGlobalSettings) {
      console.log('✅ Global default template settings already exist, skipping seed.');
      return;
    }

    // Create with explicit timestamps
    const now = new Date();
    const globalSettings = await prisma.templateSettings.create({
      data: {
        userId: null,
        reportType: 'DEEPSIGHT',
        templateType: 'GLOBAL',
        settings: DEFAULT_TEMPLATE_SETTINGS,
        updatedBy: null,
        createdAt: now,
        updatedAt: now
      }
    });

    console.log('✅ Global default template settings created successfully:');
    console.log('   - ID:', globalSettings.id);
    console.log('   - Report Type:', globalSettings.reportType);
    console.log('   - Template Type:', globalSettings.templateType);
    console.log('   - Created At:', globalSettings.createdAt);

  } catch (error) {
    console.error('❌ Error seeding template settings:', error);
    throw error;
  }
}

/**
 * Debug function to check schema
 */
async function debugSchema() {
  try {
    console.log('🔍 Debugging schema...');
    
    // Try to get the model info
    const model = prisma.templateSettings;
    console.log('✅ TemplateSettings model exists');
    
    // Try a simple findMany to see if table exists
    const existing = await prisma.templateSettings.findMany({
      take: 1
    });
    console.log('✅ TemplateSettings table exists, found records:', existing.length);
    
  } catch (error) {
    console.error('❌ Schema debug error:', error);
    console.log('💡 Suggestions:');
    console.log('   1. Run: npx prisma generate');
    console.log('   2. Run: npx prisma db push');
    console.log('   3. Check if TemplateSettings table exists in database');
  }
}

/**
 * Main seed function
 */
async function main() {
  try {
    // First debug the schema
    await debugSchema();
    
    // Try the main seeding method
    try {
      await seedTemplateSettings();
    } catch (error) {
      console.log('⚠️  Main method failed, trying alternative method...');
      await seedTemplateSettingsAlternative();
    }
    
    console.log('🎉 Template settings seeding completed successfully!');
  } catch (error) {
    console.error('💥 Template settings seeding failed:', error);
    
    // Provide helpful debugging info
    console.log('\n🔧 Debugging Steps:');
    console.log('1. Check if you ran the migration:');
    console.log('   npx prisma migrate dev');
    console.log('');
    console.log('2. Generate Prisma client:');
    console.log('   npx prisma generate');
    console.log('');
    console.log('3. Check your schema.prisma file has TemplateSettings model');
    console.log('');
    console.log('4. Check if the table exists in your database:');
    console.log('   SELECT * FROM "TemplateSettings" LIMIT 1;');
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed if this file is executed directly
  main();


export { seedTemplateSettings, DEFAULT_TEMPLATE_SETTINGS };