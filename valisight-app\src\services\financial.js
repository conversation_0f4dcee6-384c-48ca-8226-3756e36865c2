import axiosInstance from "./axiosInstance";

/**
 * Get comprehensive financial data for a company
 * Returns data for first 2 chart of accounts by default unless specific accountIds are provided
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {Array} params.accountIds - Array of account IDs to filter (optional, defaults to first 2 accounts)
 * @param {number} params.year - Year filter (optional)
 * @param {number} params.month - Month filter (optional)
 */
export const getComprehensiveFinancialData = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.accountIds && params.accountIds.length > 0) {
    params.accountIds.forEach((id) => queryParams.append("accountIds", id));
  }

  if (params.year) {
    queryParams.append("year", params.year);
  }

  if (params.month) {
    queryParams.append("month", params.month);
  }

  const queryString = queryParams.toString();
  const url = `/financial/comprehensive/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};

/**
 * Get financial data for specific account IDs
 * @param {number} companyId - Company ID
 * @param {Array} accountIds - Array of account IDs
 */
export const getFinancialDataByAccounts = async (companyId, accountIds) => {
  return await axiosInstance.post("/financial/by-accounts", {
    companyId,
    accountIds,
  });
};

/**
 * Get financial summary for a company
 * @param {number} companyId - Company ID
 */
export const getFinancialSummary = async (companyId) => {
  return await axiosInstance.get(`/financial/summary/${companyId}`);
};

/**
 * Get chart of accounts with recent financial data (limited)
 * @param {number} companyId - Company ID
 * @param {number} limit - Number of accounts to return (default: 10)
 */
export const getAccountsWithRecentData = async (companyId, limit = 10) => {
  return await axiosInstance.get(
    `/financial/accounts/${companyId}?limit=${limit}`
  );
};

/**
 * Get financial data for the first 2 accounts from chart of accounts
 * This function specifically returns comprehensive financial data for only the first 2 chart of accounts
 * @param {number} companyId - Company ID
 * @param {Object} filters - Optional filters (year, month)
 */
export const getFirst2AccountsFinancialData = async (
  companyId,
  filters = {}
) => {
  // Call comprehensive endpoint without accountIds to get first 2 accounts by default
  return await getComprehensiveFinancialData(companyId, filters);
};

/**
 * Get all financial data for current user and company
 * @param {number} companyId - Company ID
 * @param {Object} filters - Optional filters
 * @param {number} filters.year - Year filter
 * @param {number} filters.month - Month filter
 */
export const getAllFinancialData = async (companyId, filters = {}) => {
  return await getComprehensiveFinancialData(companyId, filters);
};

// ============ INDEPENDENT ENTITY FUNCTIONS ============

/**
 * Get Chart of Accounts data independently
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {Array} params.accountIds - Array of account IDs to filter (optional)
 */
export const getChartOfAccounts = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.accountIds && params.accountIds.length > 0) {
    params.accountIds.forEach((id) => queryParams.append("accountIds", id));
  }

  const queryString = queryParams.toString();
  const url = `/financial/chart-of-accounts/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};

/**
 * Get Profit & Loss data independently
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {Array} params.accountIds - Array of account IDs to filter (optional)
 * @param {number} params.year - Year filter (optional)
 * @param {number} params.month - Month filter (optional)
 */
export const getProfitLossData = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.accountIds && params.accountIds.length > 0) {
    params.accountIds.forEach((id) => queryParams.append("accountIds", id));
  }

  if (params.year) {
    queryParams.append("year", params.year);
  }

  if (params.month) {
    queryParams.append("month", params.month);
  }

  const queryString = queryParams.toString();
  const url = `/financial/profit-loss/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};

/**
 * Get Trial Balance data independently
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {Array} params.accountIds - Array of account IDs to filter (optional)
 * @param {number} params.year - Year filter (optional)
 * @param {number} params.month - Month filter (optional)
 */
export const getTrialBalanceData = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.accountIds && params.accountIds.length > 0) {
    params.accountIds.forEach((id) => queryParams.append("accountIds", id));
  }

  if (params.year) {
    queryParams.append("year", params.year);
  }

  if (params.month) {
    queryParams.append("month", params.month);
  }

  const queryString = queryParams.toString();
  const url = `/financial/trial-balance/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};

/**
 * Get Balance Sheet data independently
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {Array} params.accountIds - Array of account IDs to filter (optional)
 * @param {number} params.year - Year filter (optional)
 * @param {number} params.month - Month filter (optional)
 */
export const getBalanceSheetData = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.accountIds && params.accountIds.length > 0) {
    params.accountIds.forEach((id) => queryParams.append("accountIds", id));
  }

  if (params.year) {
    queryParams.append("year", params.year);
  }

  if (params.month) {
    queryParams.append("month", params.month);
  }

  const queryString = queryParams.toString();
  const url = `/financial/balance-sheet/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};

/**
 * Get Accounts Receivable Aging data independently
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {number} params.year - Year filter (optional)
 * @param {number} params.month - Month filter (optional)
 */
export const getARAgingData = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.year) {
    queryParams.append("year", params.year);
  }

  if (params.month) {
    queryParams.append("month", params.month);
  }

  const queryString = queryParams.toString();
  const url = `/financial/ar-aging/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};

/**
 * Get Accounts Payable Aging data independently
 * @param {number} companyId - Company ID
 * @param {Object} params - Query parameters
 * @param {number} params.year - Year filter (optional)
 * @param {number} params.month - Month filter (optional)
 */
export const getAPAgingData = async (companyId, params = {}) => {
  const queryParams = new URLSearchParams();

  if (params.year) {
    queryParams.append("year", params.year);
  }

  if (params.month) {
    queryParams.append("month", params.month);
  }

  const queryString = queryParams.toString();
  const url = `/financial/ap-aging/${companyId}${
    queryString ? `?${queryString}` : ""
  }`;

  return await axiosInstance.get(url);
};
