# Variables
DOCKER_COMPOSE := docker compose
PROJECT_NAME := fintuition-backend
COMPOSE_FILE := -f docker-compose.yml
STAGE_COMPOSE_FILE := -f stage.docker-compose.yml
APP_COMPOSE_FILE := -f app.docker-compose.yml

# Commands
# Build the Docker images (backend, db)
build:
	$(DOCKER_COMPOSE) $(COMPOSE_FILE) build

build-stage:
	$(DOCKER_COMPOSE) $(STAGE_COMPOSE_FILE) build

build-app:
	$(DOCKER_COMPOSE) $(APP_COMPOSE_FILE) build

# Start containers in detached mode
up:
	$(DOCKER_COMPOSE) $(COMPOSE_FILE) up -d

up-stage:
	$(DOCKER_COMPOSE) $(STAGE_COMPOSE_FILE) up -d

up-app:
	$(DOCKER_COMPOSE) $(APP_COMPOSE_FILE) up -d

# Stop containers
stop:
	$(DOCKER_COMPOSE) $(COMPOSE_FILE) stop

stop-stage:
	$(DOCKER_COMPOSE) $(STAGE_COMPOSE_FILE) stop

stop-app:
	$(DOCKER_COMPOSE) $(APP_COMPOSE_FILE) stop

# Stop and remove containers, networks, volumes, and images
down:
	$(DOCKER_COMPOSE) $(COMPOSE_FILE) down

down-stage:
	$(DOCKER_COMPOSE) $(STAGE_COMPOSE_FILE) down

down-app:
	$(DOCKER_COMPOSE) $(APP_COMPOSE_FILE) down

# Restart the project
restart-stage:
	$(MAKE) down-stage
	$(MAKE) build-stage
	$(MAKE) up-stage

restart-app:
	$(MAKE) down-app
	$(MAKE) build-app
	$(MAKE) up-app

restart:
	$(MAKE) down
	$(MAKE) build
	$(MAKE) up

# View logs
logs:
	$(DOCKER_COMPOSE) $(COMPOSE_FILE) logs -f $(service)

logs-stage:
	$(DOCKER_COMPOSE) $(STAGE_COMPOSE_FILE) logs -f $(service)

logs-app:
	$(DOCKER_COMPOSE) $(APP_COMPOSE_FILE) logs -f $(service)

# Run Prisma migrations (deploy existing migrations)
migrate-deploy:
	$(DOCKER_COMPOSE) $(COMPOSE_FILE) exec backend npx prisma migrate deploy

migrate-deploy-stage:
	$(DOCKER_COMPOSE) $(STAGE_COMPOSE_FILE) exec backend npx prisma migrate deploy

migrate-deploy-app:
	$(DOCKER_COMPOSE) $(APP_COMPOSE_FILE) exec backend npx prisma migrate deploy