import axiosInstance from "./axiosInstance";

export const getAllUsersList = async (payload) => {
  return await axiosInstance.get(`/shared-companies/users?page=${payload.page}&pageSize=${payload.pageSize}&search=${payload.search}`);
};

export const getSharedUsersList = async () => {
  return await axiosInstance.get(`/shared-companies/shared-users`);
};

export const addUserInSharedList = async (payload) => {
  return await axiosInstance.post(`/shared-companies/shared-users`, payload)
}

export const revokeAccess = async (id) => {
  return await axiosInstance.delete(`/shared-companies/shared-users/${id}`)
}