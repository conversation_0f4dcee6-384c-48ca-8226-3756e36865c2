import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid2 as <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import SyncIcon from "@mui/icons-material/Sync";
import UploadIcon from "@mui/icons-material/Upload";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DescriptionIcon from "@mui/icons-material/Description";
import { formatFileSize } from "../../../utils/shared";
import CircleIcon from '@mui/icons-material/Circle';


function UploadableSection({
  companyFiles,
  qboConnected,
  qboLoading = false,
  initialLoading = false,
  onUpload,
  onSync,
  syncLoading,
  companyData,
}) {
  const handleOpenModal = (type) => {
    if (qboLoading || initialLoading) return;
    onUpload(type);
  };

  const handleSync = (type) => {
    if (qboLoading || initialLoading) return;
    onSync(type);
  };

  // Calculate if buttons should be disabled
  const isDisabled = qboLoading || initialLoading || !qboConnected;
  const isUploadDisabled = qboLoading || initialLoading;

  // Helper function to get section title
  const getSectionTitle = (type) => {
    switch (type) {
      case "CHART_OF_ACCOUNTS":
        return "Chart of Accounts";
      case "TRIAL_BALANCE":
        return "Trial Balance";
      case "PROFIT_AND_LOSS":
        return "Profit and Loss";
      case "BALANCE_SHEET":
        return "Balance Sheet";
      case "AP_AGING":
        return "AP Aging";
      case "AR_AGING":
        return "AR Aging";
      default:
        return type;
    }
  };

  // Helper function to determine if section should have upload button
  const showUploadButton = (type) => {
    return type === "CHART_OF_ACCOUNTS" || type === "TRIAL_BALANCE";
  };

  // Helper function to format date to MM/DD/YYYY HH:MM AM/PM
  const formatSyncDate = (dateString) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const year = date.getFullYear();
      const hours = date.getHours();
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const ampm = hours >= 12 ? "PM" : "AM";
      const displayHours = String(hours % 12 || 12).padStart(2, "0");

      return `${month}/${day}/${year} `;
      // return `${month}/${day}/${year} ${displayHours}:${minutes} ${ampm}`;
    } catch (error) {
      return "N/A";
    }
  };

  // Helper function to get sync status
  const getSyncStatus = (lastUpdated) => {
    if (!qboConnected) return null;
    if (!lastUpdated) return "pending";
    return "synced";
  };

  // Define QBO-only sections
  const qboOnlySections = [
    // {
    //   type: "CHART_OF_ACCOUNTS",
    //   last_updated: companyData?.ChartOfAccountsLastSyncDate || null,
    //   documents: [],
    // },
    // {
    //   type: "TRIAL_BALANCE",
    //   last_updated: companyData?.TrialBalanceLastSyncDate || null,
    //   documents: [],
    // },
    {
      type: "PROFIT_AND_LOSS",
      last_updated: companyData?.ProfitLossReportLastSyncDate || null,
      documents: [],
    },
    {
      type: "BALANCE_SHEET",
      last_updated: companyData?.BalanceSheetReportLastSyncDate || null,
      documents: [],
    },
    {
      type: "AP_AGING",
      last_updated: companyData?.APReportLastSyncDate || null,
      documents: [],
    },
    {
      type: "AR_AGING",
      last_updated: companyData?.ARReportLastSyncDate || null,
      documents: [],
    },
  ];

  // Combine company files with QBO-only sections
  const allSections = [...(companyFiles || []), ...qboOnlySections];

  const DocumentCard = ({ section }) => {
    const hasFileUpload = showUploadButton(section?.type);
    section.last_updated =
      section.type === "CHART_OF_ACCOUNTS"
        ? companyData?.TransactionReportLastSyncDate
        : section.type === "TRIAL_BALANCE"
          ? companyData?.TrialBalanceReportLastSyncDate
          : section?.last_updated;
    const syncStatus = getSyncStatus(section?.last_updated);
    const lastUploadedFile =
      section?.documents?.length > 0 ? section?.documents[0]?.file_name : null;

    return (
      <Card
        sx={{
          p: 2,
          height: "fit-content",
          border: "1px solid #e5e7eb",
          borderRadius: 1,
          boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
          "&:hover": {
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
          },
          transition: "all 0.2s ease-in-out",
          backgroundColor: "#fff",
        }}
      >
        {/* Header with title and status*/}
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{ mb: 1 }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: 16,
              color: "#111827",
            }}
          >
            {getSectionTitle(section?.type)}
          </Typography>

          {/* Status indicators - now at the far right */}
          {syncStatus === "synced" && (
            <Chip
              icon={<CircleIcon sx={{ fontSize: "8px !important" }} />}
              label="Synced"
              size="small"
              sx={{
                backgroundColor: "#d1fae5",
                color: "#065f46",
                border: "none",
                height: 24,
                fontSize: 12,
                fontWeight: 500,
                borderRadius: "12px",
                "& .MuiChip-icon": {
                  color: "#10b981",
                  marginLeft: "6px",
                },
                "& .MuiChip-label": {
                  paddingLeft: "9px",
                  paddingRight: "8px",
                },
              }}
            />
          )}
          {syncStatus === "pending" && (
            <Chip
              icon={<AccessTimeIcon sx={{ fontSize: "12px !important" }} />}
              label="Pending"
              size="small"
              sx={{
                backgroundColor: "#fef3c7",
                color: "#92400e",
                border: "none",
                height: 24,
                fontSize: 12,
                fontWeight: 500,
                borderRadius: "12px",
                "& .MuiChip-icon": {
                  color: "#f59e0b",
                  marginLeft: "6px",
                },
                "& .MuiChip-label": {
                  paddingLeft: "9px",
                  paddingRight: "8px",
                },
              }}
            />
          )}
        </Stack>

        {/* File information */}
        {hasFileUpload && lastUploadedFile && (
          <Box sx={{ mb: 1.5 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 0.5, fontSize: 12, color: "#6b7280" }}
            >
              Last uploaded file:
            </Typography>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 400,
                color: "#111827",
                fontSize: 13,
                wordBreak: "break-word",
              }}
            >
              {lastUploadedFile.length > 40
                ? `${lastUploadedFile.slice(0, 40)}...`
                : lastUploadedFile}
            </Typography>
          </Box>
        )}

        {/* Last sync information */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="caption"
            sx={{ fontSize: 12, fontWeight: 500, color: "#4b5563" }}
          >
            Last sync:{" "}
            {section?.type === ""
              ? "N/A"
              : formatSyncDate(section?.last_updated)}
          </Typography>
        </Box>

        {/* Additional files section */}
        {hasFileUpload && section?.documents?.length > 1 && (
          <Box sx={{ mt: 2, pt: 2, borderTop: "1px solid #e5e7eb" }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                display: "block",
                mb: 1,
                fontSize: 12,
                fontWeight: 500,
                color: "#6b7280",
              }}
            >
              Additional files ({section.documents.length - 1}):
            </Typography>
            <Stack spacing={1} sx={{ maxHeight: 120, overflow: "auto" }}>
              {section.documents.slice(1).map((document, idx) => (
                <Box
                  key={idx}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    p: 1,
                    backgroundColor: "#f9fafb",
                    borderRadius: 1,
                    border: "1px solid #e5e7eb",
                  }}
                >
                  <DescriptionIcon sx={{ fontSize: 16, color: "#6b7280" }} />
                  <Tooltip title={document?.file_name || ""} arrow>
                    <Typography
                      variant="body2"
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        flex: 1,
                        cursor: "pointer",
                        fontSize: 12,
                        color: "#374151",
                      }}
                    >
                      {document?.file_name}
                    </Typography>
                  </Tooltip>
                  <Typography
                    variant="caption"
                    sx={{ fontSize: 11, color: "#6b7280" }}
                  >
                    {formatFileSize(parseInt(document?.size))}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Box>

        )}

        <Box sx={{ mt: 2 }}>
  {/* Action buttons */}
  <Stack direction="row" spacing={1}>
    <Button
      variant="outlined"
      onClick={() => handleSync(section?.type)}
      disabled={isDisabled || syncLoading[section?.type]}
      sx={{
        width: syncLoading[section?.type] ? 120 : 80,
  ...(syncLoading[section?.type] && {
    paddingX: 2,
          cursor: "not-allowed",
          animation: "pulse 2s infinite",
          "@keyframes pulse": {
            "0%": { opacity: 0.8 },
            "50%": { opacity: 1 },
            "100%": { opacity: 0.8 },
          },
          // Disable hover effects during loading
          "&:hover": {
            transform: "none",
          },
        }),

        "&:disabled": {
          cursor: "not-allowed",
        },
      }}
    >
      {syncLoading[section?.type] ? "Syncing..." : "Sync"}
    </Button>
    {showUploadButton(section?.type) && (
      <Button
        variant="contained"
        color="primary"
        disabled={isUploadDisabled}
        onClick={() => handleOpenModal(section?.type)}
        sx={{
          width: 80, // Fixed width for Upload button
        }}
      >
        Upload
      </Button>
    )}
  </Stack>
</Box>
      </Card>
    );
  };

  return (
    <Box sx={{ p: 0 }}>
      <Grid container spacing={4}>
        {allSections?.map((section, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 6, lg: 4 }} key={index}>
            <DocumentCard section={section} />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}

export default UploadableSection;
