import * as sharedCompaniesService from "../services/sharedCompanies.service.js"
import { sharedCompaniesValidation } from "../validations/sharedCompanies.validation.js";



// Helper function for validation
const validateRequest = (schema, data) => {
  const { error } = schema.validate(data);
  if (error) {
    throw {
      status: 200,
      statusCode: 400,
      success: false,
      message: error.details[0].message,
    };
  }
};

// Helper function for error handling
const handleError = (error, res) => {
  const { status } = error;
  const s = status ? status : 500;
  res.status(s).json({
    success: error.success,
    statusCode: error.statusCode,
    message: error.message,
  });
};

export const getAllUsers = async (req, res) => {
  try {
    let { id: userId } = req.user;
    let { page, pageSize, search } = req.query;
    // add validation
    let { error } = sharedCompaniesValidation.getAllUsers.validate({ page, pageSize, userId });
    if (error) {
      throw {
        status: 200,
        statusCode: 400,
        success: false,
        message: error.details[0].message,
      };
    }

    // Set default values
    const DEFAULT_PAGE = 1;
    const DEFAULT_PAGE_SIZE = 15;

    page = page && page > 0 ? parseInt(page, 10) : DEFAULT_PAGE;
    pageSize = pageSize && pageSize > 0 ? parseInt(pageSize, 10) : DEFAULT_PAGE_SIZE;
    search = search ? search : ""

    res.json(await sharedCompaniesService.getAllUsers(userId, page, pageSize, search));
  } catch (error) {
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
}


export const addSharedUser = async (req, res) => {
  try {
    let { userId } = req.body
    let { id: currentUserId } = req.user

    // Validate request
    validateRequest(sharedCompaniesValidation.addSharedUser, { userId });
    res.json(await sharedCompaniesService.addSharedUser(userId, currentUserId));
  } catch (error) {
    handleError(error, res);
  }
}



export const getSharedUsers = async (req, res) => {
  try {
    let { id: userId } = req.user
    let { page, pageSize } = req.query;

    let { error } = sharedCompaniesValidation.getAllUsers.validate({ page, pageSize, userId });
    if (error) {
      throw {
        status: 200,
        statusCode: 400,
        success: false,
        message: error.details[0].message,
      };
    }

    // Set default values
    const DEFAULT_PAGE = 1;
    const DEFAULT_PAGE_SIZE = 10;

    page = page && page > 0 ? parseInt(page, 10) : DEFAULT_PAGE;
    pageSize = pageSize && pageSize > 0 ? parseInt(pageSize, 10) : DEFAULT_PAGE_SIZE;

    res.json(await sharedCompaniesService.getSharedUser(userId, page, pageSize));
  } catch (error) {
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
}


export const deleteSharedUser = async (req, res) => {
  try {
    let { id: userId } = req.user
    let { id: targetUserId } = req.params;
    res.json(await sharedCompaniesService.deleteSharedUser(userId, parseInt(targetUserId)));
  } catch (error) {
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
}
