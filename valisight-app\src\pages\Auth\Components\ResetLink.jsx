import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import logo from "../../../assets/logo2.svg";
import { resetPasswordLink } from "../../../services/auth";
import Cookies from "js-cookie";
import CircularProgress from "@mui/material/CircularProgress";

const ResetLinkPwd = () => {
  const [isloading, setIsloading] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);

  const handleResetPassword = async () => {
    try {
      setIsloading(true);

      const email = Cookies.get("email");

      if (!email) {
        throw new Error("No token found in cookies");
      }

      await resetPasswordLink(email);

      setResetSuccess(true);
      setIsloading(false);
    } catch (error) {
      // Show error toast if something goes wrong
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: error.message || "Failed to send reset password link.",
      });
    }
  };

  return (
    <div className="flex items-center justify-center h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
        <div className="flex justify-center mb-6">
          <img src={logo} alt="Logo" className="w-12 h-12" />
        </div>

        <h2 className="text-2xl font-semibold text-center  mb-8">
          Reset Password Link
        </h2>

        {!resetSuccess ? (
          <button
            onClick={handleResetPassword}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {isloading ? (
              <CircularProgress size={20} sx={{ color: "white" }} />
            ) : (
              "Send Reset Password Link"
            )}
          </button>
        ) : (
          <div className="text-center text-green-500">
            <p>Your reset password link has been sent to your email.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResetLinkPwd;
