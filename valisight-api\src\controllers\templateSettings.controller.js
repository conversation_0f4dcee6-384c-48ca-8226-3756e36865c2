import * as templateSettingsService from '../services/templateSettings.service.js';

/**
 * GET /api/template-settings
 * Get template settings for the current user
 */
export const getTemplateSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const reportType = req.query.reportType || 'DEEPSIGHT';

    const result = await templateSettingsService.getTemplateSettings(userId, reportType);

    if (!result.success) {
      return res.status(result.statusCode).json({
        success: false,
        message: result.message
      });
    }

    res.status(result.statusCode).json({
      success: true,
      message: result.message,
      data: result.data,
      user: {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email,
        isAdmin: req.user.isAdmin
      }
    });

  } catch (error) {
    console.error('Error in getTemplateSettings controller:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching template settings'
    });
  }
};

//get global settings
export const getGlobalTemplateSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const reportType = req.query.reportType || 'DEEPSIGHT';

    const result = await templateSettingsService.getGlobalTemplateSettings(userId, reportType);

    if (!result.success) {
      return res.status(result.statusCode).json({
        success: false,
        message: result.message
      });
    }

    res.status(result.statusCode).json({
      success: true,
      message: result.message,
      data: result.data
    });

  } catch (error) {
    console.error('Error in getGlobalTemplateSettings controller:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching template settings'
    });
  }
};


/**
 * PUT /api/template-settings
 * Update template settings
 * - Admin: Updates global settings (userId set to null)
 * - Regular user: Creates/updates custom settings
 */
export const updateTemplateSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const isAdmin = req.user.isAdmin;
    const { settings, reportType } = req.body;

    // Validate request body
    if (!settings) {
      return res.status(400).json({
        success: false,
        message: 'Settings object is required in request body'
      });
    }

    const result = await templateSettingsService.updateTemplateSettings(
      userId,
      settings,
      isAdmin,
      reportType || 'DEEPSIGHT'
    );

    if (!result.success) {
      return res.status(result.statusCode).json({
        success: false,
        message: result.message
      });
    }

    res.status(result.statusCode).json({
      success: true,
      message: result.message,
      data: result.data
    });

  } catch (error) {
    console.error('Error in updateTemplateSettings controller:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while updating template settings'
    });
  }
};

