import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Switch,
  Card,
  Divider,
  TextField,
  Button,
  useTheme,
  alpha,
  Snackbar,
  Alert,
  CircularProgress,
  RadioGroup,
  FormControlLabel,
  Radio,
  Collapse,
  IconButton,
  Tooltip,
} from '@mui/material';

import {
  ScheduleOutlined as ComingSoonIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';

import {
  getContentSettingsByReportType,
  updateContentSettings
} from '../../../services/contentSettings';

// Fallback component for unsupported report types
const ComingSoonFallback = ({ reportType, reportTypeOptions }) => {
  const theme = useTheme();
  
  const selectedReportLabel = reportTypeOptions.find(
    option => option.value === reportType
  )?.label || reportType;

  return (
    <Box sx={{ width: '100%' }}>
      <Card
        elevation={0}
        sx={{
          mb: 3,
          p: 4,
          backgroundColor: alpha('#1976d2', 0.05),
          border: `1px solid ${alpha('#1976d2', 0.2)}`,
          borderRadius: 2,
          textAlign: 'center',
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <ComingSoonIcon sx={{ color: '#1976d2', fontSize: '3rem' }} />
          <Box>
            <Typography
              variant="h5"
              sx={{
                color: '#1976d2',
                fontWeight: 600,
                fontSize: '1.3rem',
                mb: 1,
              }}
            >
              {selectedReportLabel} - Coming Soon!
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: 'text.secondary',
                fontSize: '1rem',
                maxWidth: '500px',
                margin: '0 auto',
              }}
            >
              This report type will be implemented soon.
            </Typography>
          </Box>
        </Box>
      </Card>
    </Box>
  );
};

// Collapsible Card Component
const CollapsibleCard = ({ section, settings, handleSwitchChange, isExpanded, onToggle }) => {
  const handleClick = (e) => {
    e.stopPropagation();
    onToggle(section.key);
  };

  return (
    <Card
      elevation={0}
      sx={{
        border: `1px solid ${alpha('#e0e0e0', 0.8)}`,
        borderRadius: 2,
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          boxShadow: `0 2px 8px ${alpha('#1976d2', 0.1)}`,
          borderColor: alpha('#1976d2', 0.3),
        },
      }}
    >
      {/* Card Header - Clickable */}
      <Box
        onClick={handleClick}
        sx={{
          p: 3,
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          '&:hover': {
            backgroundColor: alpha('#1976d2', 0.02),
          },
        }}
      >
        <Box sx={{ flex: 1 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              fontSize: '1.1rem',
              mb: 0.5,
            }}
          >
            {section.title}
          </Typography>
          <Typography
            variant="body2"
            sx={{ 
              color: 'text.secondary',
              fontSize: '0.875rem',
            }}
          >
            {section.description}
          </Typography>
        </Box>
        <IconButton
          size="small"
          sx={{
            transition: 'transform 0.3s ease',
            transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
          }}
        >
          <ExpandMoreIcon />
        </IconButton>
      </Box>

      {/* Collapsible Content */}
      <Collapse in={isExpanded} timeout="auto" unmountOnExit>
        <Divider />
        <Box sx={{ p: 3, pt: 2, height: '360px', overflowY: 'auto' }}>
          {section.options.map((option, index) => (
            <Box 
              key={option.key}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                py: 1.5,
                borderBottom: index < section.options.length - 1 ? '1px solid' : 'none',
                borderBottomColor: alpha('#e0e0e0', 0.4),
              }}
            >
              <Tooltip 
                title={option.tooltip}
                arrow
                placement="top-start"
                sx={{
                  '& .MuiTooltip-tooltip': {
                    fontSize: '0.875rem',
                    maxWidth: '400px',
                    lineHeight: 1.4,
                  }
                }}
              >
                <Box sx={{ 
                  flex: 1,
                  '&:hover': {
                    backgroundColor: alpha('#1976d2', 0.04),
                    borderRadius: 1,
                    padding: '6px 10px',
                    margin: '-6px -10px',
                  }
                }}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 500,
                      color: 'text.primary',
                      fontSize: '0.9rem',
                      lineHeight: 1.3,
                    }}
                  >
                    {option.label}
                  </Typography>
                </Box>
              </Tooltip>

              <Switch
                checked={settings[option.key]}
                onChange={handleSwitchChange(option.key)}
                inputProps={{ 'aria-label': 'controlled' }}
                sx={{ ml: 2 }}
              />
            </Box>
          ))}
        </Box>
      </Collapse>
    </Card>
  );
};

const ReportSettings = ({ companyId }) => {
  const theme = useTheme();
  const [settings, setSettings] = useState({
    reportType: 'DEEPSIGHT',
    incomeSummary: true,
    netIncome: true,
    grossProfitMargin: true,
    netProfitMargin: true,
    roaAndRoe: true,
    expensesTopAccounts: true,
    expensesTopAccountsMonthly: true,
    expensesWagesVsRevenueMonthly: true,
    daysSalesOutstanding: true,
    daysPayablesOutstanding: true,
    daysInventoryOutstanding: true,
    cashConversionCycle: true,
    fixedAssetTurnover: true,
    netChangeInCash: true,
    quickRatio: true,
    monthsCashOnHand: true,
    thirteenMonthTrailing: true,
    monthly: true,
    ytd: true,
    balanceSheet: true,
    prompt: "",
  });

  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [hasChanges, setHasChanges] = useState(false);
  const [expandedCards, setExpandedCards] = useState({});

  // Toggle individual card
  const toggleCard = (cardKey) => {
    setExpandedCards(prev => ({
      ...prev,
      [cardKey]: !prev[cardKey]
    }));
  };

  // Report type options
  const reportTypeOptions = [
    {
      value: 'DEEPSIGHT',
      label: 'Deepsight'
    },
    {
      value: "profitpulse",
      label: 'ProfitPulse (Monthly)'
    },
    {
      value: "kpitrack",
      label: 'KPITrack (Benchmark)'
    },
    {
      value: "gaap",
      label: 'GAAP Align'
    },
    {
      value: "fincheck",
      label: 'FinCheck (Current State)'
    },
    {
      value: "flowcast",
      label: 'FlowCast (13 Week)'
    }
  ];

  // Settings sections configuration
  const settingsSections = [
    {
      key: 'currentFiscalYear',
      title: 'Current Fiscal Year',
      description: 'Key performance metrics for the current fiscal period',
      options: [
        {
          key: 'incomeSummary',
          label: 'Monthly Performance Breakdown (Income Summary)',
          tooltip: 'Provides a monthly breakdown of income performance showing revenue trends and patterns throughout the fiscal year.'
        },
        {
          key: 'netIncome',
          label: 'Net Income/(Loss)',
          tooltip: 'Shows the companys bottom-line profit or loss after deducting all expenses, taxes, and interest for the current fiscal year.'
        },
        {
          key: 'grossProfitMargin',
          label: 'Gross Profit Margin',
          tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'
        },
        {
          key: 'netProfitMargin',
          label: 'Net Profit Margin',
          tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'
        },
      ]
    },
    {
      key: 'expenseSummary',
      title: 'Expense Summary',
      description: 'Detailed analysis of company expenses and efficiency ratios',
      options: [
        {
          key: 'roaAndRoe',
          label: 'Return on Assets and Equity',
          tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'
        },
        {
          key: 'expensesTopAccounts',
          label: 'Expenses: Top Accounts',
          tooltip: 'Identifies and analyzes the highest expense categories, helping prioritize cost management efforts.'
        },
        {
          key: 'expensesTopAccountsMonthly',
          label: 'Expenses: Top Accounts Monthly',
          tooltip: 'Monthly breakdown of top expense accounts showing spending patterns and trends over time.'
        },
        {
          key: 'expensesWagesVsRevenueMonthly',
          label: 'Expenses: Wages Vs Revenue Monthly',
          tooltip: 'Compares monthly wage expenses against revenue to track labor cost efficiency and productivity trends.'
        },
      ]
    },
    {
      key: 'operationalEfficiency',
      title: 'Operational Efficiency',
      description: 'Key operational metrics measuring business efficiency',
      options: [
        {
          key: 'daysSalesOutstanding',
          label: 'Days Sales (A/R) Outstanding',
          tooltip: 'Measures the average number of days it takes to collect receivables. Lower values indicate faster cash collection.'
        },
        {
          key: 'daysPayablesOutstanding',
          label: 'Days Payables (AP) Outstanding',
          tooltip: 'Shows the average number of days the company takes to pay suppliers. Helps assess payment timing and cash flow management.'
        },
        {
          key: 'daysInventoryOutstanding',
          label: 'Days Inventory Outstanding',
          tooltip: 'Measures how many days of inventory the company holds on average. Lower values may indicate efficient inventory management.'
        },
        {
          key: 'cashConversionCycle',
          label: 'Cash Conversion Cycle',
          tooltip: 'The time it takes to convert inventory investments into cash flows from sales. Shorter cycles indicate better working capital management.'
        },
        {
          key: 'fixedAssetTurnover',
          label: 'Fixed Asset Turnover',
          tooltip: 'Measures how efficiently the company uses its fixed assets to generate sales. Higher ratios indicate better asset utilization.'
        },
      ]
    },
    {
      key: 'liquiditySummary',
      title: 'Liquidity Summary',
      description: 'Cash flow and liquidity position analysis',
      options: [
        {
          key: 'netChangeInCash',
          label: 'Net Change in Cash',
          tooltip: 'Shows the overall increase or decrease in cash position over the period, indicating cash flow health.'
        },
        {
          key: 'quickRatio',
          label: 'Quick Ratio',
          tooltip: 'Measures the company\'s ability to pay short-term debts using the most liquid assets. Higher ratios indicate better liquidity.'
        },
        {
          key: 'monthsCashOnHand',
          label: 'Months Cash on Hand',
          tooltip: 'Indicates how many months the company can operate with current cash reserves, providing insight into financial runway.'
        },
      ]
    },
    {
      key: 'profitAndLoss',
      title: 'Profit and Loss',
      description: 'Comprehensive profit and loss statement analysis',
      options: [
        {
          key: 'thirteenMonthTrailing',
          label: '13 Month Trailing',
          tooltip: 'Provides a 13-month trailing view of profit and loss, smoothing seasonal variations and showing longer-term trends.'
        },
        {
          key: 'monthly',
          label: 'Monthly',
          tooltip: 'Monthly profit and loss breakdown showing month-to-month performance and identifying seasonal patterns.'
        },
        {
          key: 'ytd',
          label: 'YTD',
          tooltip: 'Year-to-date profit and loss summary comparing current performance against the full fiscal year.'
        },
      ]
    },
    {
      key: 'balanceSheet',
      title: 'Balance Sheet',
      description: 'Complete balance sheet analysis and position summary',
      options: [
        {
          key: 'balanceSheet',
          label: 'Balance Sheet',
          tooltip: 'Comprehensive balance sheet showing assets, liabilities, and equity positions with period-over-period comparisons.'
        },
      ]
    },
  ];

  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';

  // Load existing settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      if (!companyId) return;

      try {
        setIsLoading(true);
        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');

        if (response.data.success && response.data.data) {
          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;
          
          if (data && data.chartSettings) {
            setSettings(prev => ({
              ...prev,
              ...data.chartSettings,
              prompt: data.promptDescription || prev.prompt,
            }));
          }
        }
      } catch (error) {
        setErrorMessage('Failed to load settings');
        setShowError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [companyId]);

  const handleSwitchChange = (field) => (event) => {
    setSettings(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
    setHasChanges(true);
  };

  const handlePromptChange = (event) => {
    setSettings(prev => ({
      ...prev,
      prompt: event.target.value
    }));
    setHasChanges(true);
  };

  const handleReportTypeChange = async (event) => {
    const newReportType = event.target.value;
    setSettings(prev => ({
      ...prev,
      reportType: newReportType
    }));

    if (newReportType !== 'DEEPSIGHT') {
      setHasChanges(false);
      return;
    }

    if (!companyId) return;

    try {
      setIsLoading(true);
      const response = await getContentSettingsByReportType(companyId, newReportType);

      if (response.data.success && response.data.data) {
        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;
        
        if (data && data.chartSettings) {
          setSettings(prev => ({
            ...prev,
            ...data.chartSettings,
            prompt: data.promptDescription || '',
          }));
        }
      }
      setHasChanges(false);
    } catch (error) {
      setErrorMessage('Failed to load settings');
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!companyId || !isCurrentReportTypeSupported) return;

    setIsSaving(true);

    try {
      const { reportType, prompt, ...chartSettings } = settings;
      
      const payload = {
        chartSettings,
        promptDescription: prompt,
      };

      await updateContentSettings(companyId, settings.reportType, payload);

      setHasChanges(false);
      setShowSuccess(true);
    } catch (error) {
      setErrorMessage(error.response?.data?.message || 'Failed to save settings');
      setShowError(true);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  const handleCloseError = () => {
    setShowError(false);
    setErrorMessage('');
  };

  if (isLoading) {
    return (
      <Box sx={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header Section with Radio Buttons */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 600,
            color: 'text.primary',
            fontSize: '1rem',
            mb: 2,
          }}
        >
          Report Type
        </Typography>
        
        <RadioGroup
          value={settings.reportType}
          onChange={handleReportTypeChange}
          sx={{
            display: 'flex',
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          {reportTypeOptions.map((option) => (
            <Card
              key={option.value}
              elevation={0}
              sx={{
                // border: `2px solid ${
                //   settings.reportType === option.value
                //     ? '#1976d2'
                //     : alpha('#e0e0e0', 0.8)
                // }`,
                // borderRadius: 2,
                transition: 'all 0.2s ease-in-out',
                cursor: 'pointer',
                backgroundColor:
                  settings.reportType === option.value
                    ? alpha('#1976d2', 0.05)
                    : 'white',
                '&:hover': {
                  borderColor: '#1976d2',
                  boxShadow: `0 2px 8px ${alpha('#1976d2', 0.15)}`,
                },
              }}
            >
              <FormControlLabel
                value={option.value}
                control={
                  <Radio
                    sx={{
                      color: 'grey.400',
                      '&.Mui-checked': {
                        color: '#1976d2',
                      },
                    }}
                  />
                }
                label={
                  <Typography
                    sx={{
                      fontWeight: settings.reportType === option.value ? 600 : 500,
                      fontSize: '0.875rem',
                      color:
                        settings.reportType === option.value
                          ? '#1976d2'
                          : 'text.primary',
                    }}
                  >
                    {option.label}
                  </Typography>
                }
                sx={{
                  m: 0,
                  px: 2,
                  py: 1.5,
                  width: '100%',
                }}
              />
            </Card>
          ))}
        </RadioGroup>
      </Box>

      {/* Conditional Rendering */}
      {!isCurrentReportTypeSupported ? (
        <ComingSoonFallback 
          reportType={settings.reportType} 
          reportTypeOptions={reportTypeOptions} 
        />
      ) : (
        <>
          {/* Report Components Section with Collapsible Cards */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: '1rem',
                mb: 1,
              }}
            >
              Report Components
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: 'text.secondary', mb: 3 }}
            >
              Select which financial metrics to include in your analysis
            </Typography>

            {/* Grid Layout for Collapsible Sections */}
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: { xs: '1fr', md: '1fr 1fr 1fr' }, 
              gap: 3,
              maxWidth: '100%',
              alignItems: 'start'
            }}>
              {settingsSections.map((section) => (
                <CollapsibleCard
                  key={section.key}
                  section={section}
                  settings={settings}
                  handleSwitchChange={handleSwitchChange}
                  isExpanded={expandedCards[section.key] || false}
                  onToggle={toggleCard}
                />
              ))}
            </Box>
          </Box>

          {/* Custom Analysis Prompt Section */}
          <Card
            elevation={0}
            sx={{
              mb: 3,
              width: 590,
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: alpha('#1976d2', 0.02),
              },
            }}
          >
            <Box
              component="fieldset"
              sx={{
                border: '1.5px solid',
                borderColor: '#e5e7eb',
                borderRadius: 2,
                margin: 0,
                position: 'relative',
                backgroundColor: 'white',
                transition: 'border-color 0.2s ease-in-out',
                '&:focus-within': {
                  borderColor: '#1976d2',
                },
                '&:focus-within legend': {
                  color: '#1976d2',
                },
              }}
            >
              <Box
                component="legend"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  fontFamily: '"Roboto","Helvetica","Arial",sans-serif',
                  color: 'text.secondary',
                  padding: '0 2px',
                  marginLeft: 1,
                  transition: 'color 0.2s ease-in-out',
                }}
              >
                Prompt
              </Box>
              
              <TextField
                multiline
                rows={5}
                value={settings.prompt}
                onChange={handlePromptChange}
                placeholder="Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025"
                fullWidth
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      border: 'none',
                    },
                    '&:hover fieldset': {
                      border: 'none',
                    },
                    '&.Mui-focused fieldset': {
                      border: 'none',
                    },
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.875rem',
                    lineHeight: 1.6,
                    '&::placeholder': {
                      color: 'text.secondary',
                      opacity: 0.7,
                    },
                  },
                }}
              />
            </Box>
          </Card>

          {/* Save Button */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-start' }}>
            <Button
              variant="contained"
              size="medium"
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              sx={{
                textTransform: 'none',
                minWidth: '120px',
                px: 3,
              }}
            >
              {isSaving ? 'SAVING...' : 'SAVE'}
            </Button>
          </Box>
        </>
      )}

      {/* Success Snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={4000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSuccess}
          severity="success"
          variant="filled"
          sx={{
            backgroundColor: '#1976d2',
            '& .MuiAlert-icon': {
              color: 'white',
            },
          }}
        >
          Settings saved successfully!
        </Alert>
      </Snackbar>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseError}
          severity="error"
          variant="filled"
        >
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ReportSettings;