// frontend\src\services\qbo.js
import axiosInstance from "./axiosInstance";

export const connectQBO = async (companyId) => {
  return await axiosInstance.get(`/qbo/connect?companyId=${companyId}`);
};

export const getQBOStatus = async (companyId) => {
  return await axiosInstance.get(`/qbo/status?companyId=${companyId}`);
};

export const disconnectQBO = async (companyId) => {
  return await axiosInstance.post(`/qbo/disconnect?companyId=${companyId}`);
};
export const syncAccounts = async (data) => {
  return await axiosInstance.post(`/qbo/sync/accounts`, data);
};

export const syncTrialBalance = async (data) => {
  return await axiosInstance.post(`/qbo/sync/trial-balance`, data);
};

export const syncProfitLoss = async (data) => {
  return await axiosInstance.post(`/qbo/sync/profit-loss`, data);
};

export const syncBalanceSheet = async (data) => {
  return await axiosInstance.post(`/qbo/sync/balance-sheet`, data);
};

export const syncAPAging = async (data) => {
  return await axiosInstance.post(`/qbo/sync/ap-aging`, data);
};

export const syncARAging = async (data) => {
  return await axiosInstance.post(`/qbo/sync/ar-aging`, data);
};
