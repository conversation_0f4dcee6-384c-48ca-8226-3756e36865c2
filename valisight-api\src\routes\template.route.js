
import { Router } from 'express';
export const templateRoute = Router();
import * as templateController from '../controllers/template.controller.js';
import { upload } from '../utils/multerConfig.js';
import { authenticate } from '../middleware/auth.middleware.js';

templateRoute.post('/', upload.array('files', 5), templateController.template);
templateRoute.get('/', authenticate, templateController.getTemplates);
templateRoute.get('/all', templateController.getTemplates); //temp for admin get all without token
templateRoute.get('/:id/download', templateController.downloadTemplateFiles);
templateRoute.delete('/:id', templateController.deleteTemplate);
templateRoute.put('/:templateId', upload.array('files', 5), templateController.updateTemplate);