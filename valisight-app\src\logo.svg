<svg width="58" height="57" viewBox="0 0 58 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_4_18554)">
<g clip-path="url(#clip0_4_18554)">
<rect x="5" y="3" width="48" height="48" rx="12" fill="white"/>
<rect x="5" y="3" width="48" height="48" rx="12" fill="url(#paint0_linear_4_18554)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.9997 6.05825C17.4339 6.05825 8.05791 15.4342 8.05791 27C8.05791 38.5658 17.4339 47.9418 28.9997 47.9418C40.5655 47.9418 49.9414 38.5658 49.9414 27C49.9414 15.4342 40.5655 6.05825 28.9997 6.05825ZM7.94141 27C7.94141 15.3699 17.3695 5.94175 28.9997 5.94175C40.6298 5.94175 50.0579 15.3699 50.0579 27C50.0579 38.6302 40.6298 48.0583 28.9997 48.0583C17.3695 48.0583 7.94141 38.6302 7.94141 27Z" fill="#D0D5DD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29 21.1168C25.7506 21.1168 23.1165 23.751 23.1165 27.0003C23.1165 30.2497 25.7506 32.8838 29 32.8838C32.2494 32.8838 34.8835 30.2497 34.8835 27.0003C34.8835 23.751 32.2494 21.1168 29 21.1168ZM23 27.0003C23 23.6866 25.6863 21.0003 29 21.0003C32.3137 21.0003 35 23.6866 35 27.0003C35 30.3141 32.3137 33.0003 29 33.0003C25.6863 33.0003 23 30.3141 23 27.0003Z" fill="#D0D5DD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.0002 22.9801C26.7803 22.9801 24.9808 24.7797 24.9808 26.9995C24.9808 29.2194 26.7803 31.019 29.0002 31.019C31.22 31.019 33.0196 29.2194 33.0196 26.9995C33.0196 24.7797 31.22 22.9801 29.0002 22.9801ZM24.8643 26.9995C24.8643 24.7153 26.716 22.8636 29.0002 22.8636C31.2844 22.8636 33.1361 24.7153 33.1361 26.9995C33.1361 29.2838 31.2844 31.1355 29.0002 31.1355C26.716 31.1355 24.8643 29.2838 24.8643 26.9995Z" fill="#D0D5DD"/>
<path d="M28.9414 3H29.0579V51H28.9414V3Z" fill="#D0D5DD"/>
<path d="M53 26.9414L53 27.0579L5 27.0579L5 26.9414L53 26.9414Z" fill="#D0D5DD"/>
<path d="M44.9033 3H45.0198V51H44.9033V3Z" fill="#D0D5DD"/>
<path d="M20.9609 3H21.0774V51H20.9609V3Z" fill="#D0D5DD"/>
<path d="M36.9229 3H37.0394V51H36.9229V3Z" fill="#D0D5DD"/>
<path d="M12.9805 3H13.097V51H12.9805V3Z" fill="#D0D5DD"/>
<path d="M53 42.9023L53 43.0188L5 43.0188L5 42.9023L53 42.9023Z" fill="#D0D5DD"/>
<path d="M53 18.9609L53 19.0774L5 19.0774L5 18.9609L53 18.9609Z" fill="#D0D5DD"/>
<path d="M53 34.9219L53 35.0384L5 35.0384L5 34.9219L53 34.9219Z" fill="#D0D5DD"/>
<path d="M53 10.9805L53 11.097L5 11.097L5 10.9805L53 10.9805Z" fill="#D0D5DD"/>
<g filter="url(#filter1_dd_4_18554)">
<circle cx="29" cy="27" r="12" fill="url(#paint1_linear_4_18554)"/>
</g>
<g filter="url(#filter2_b_4_18554)">
<path d="M5 27H53V31.8C53 38.5206 53 41.8809 51.6921 44.4479C50.5416 46.7058 48.7058 48.5416 46.4479 49.6921C43.8809 51 40.5206 51 33.8 51H24.2C17.4794 51 14.1191 51 11.5521 49.6921C9.29417 48.5416 7.4584 46.7058 6.30792 44.4479C5 41.8809 5 38.5206 5 31.8V27Z" fill="white" fill-opacity="0.2"/>
</g>
</g>
<rect x="5.15" y="3.15" width="47.7" height="47.7" rx="11.85" stroke="#D0D5DD" stroke-width="0.3"/>
</g>
<defs>
<filter id="filter0_dd_4_18554" x="0.5" y="0" width="57" height="57" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.5"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4_18554"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.5"/>
<feGaussianBlur stdDeviation="2.25"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_4_18554" result="effect2_dropShadow_4_18554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4_18554" result="shape"/>
</filter>
<filter id="filter1_dd_4_18554" x="12.5" y="12" width="33" height="33" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.5"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4_18554"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.5"/>
<feGaussianBlur stdDeviation="2.25"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_4_18554" result="effect2_dropShadow_4_18554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4_18554" result="shape"/>
</filter>
<filter id="filter2_b_4_18554" x="-2.5" y="19.5" width="63" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.75"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4_18554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_4_18554" result="shape"/>
</filter>
<linearGradient id="paint0_linear_4_18554" x1="29" y1="3" x2="29" y2="51" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0D5DD"/>
</linearGradient>
<linearGradient id="paint1_linear_4_18554" x1="23" y1="39" x2="35" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="#033BD7"/>
<stop offset="1" stop-color="#0495F1"/>
</linearGradient>
<clipPath id="clip0_4_18554">
<rect x="5" y="3" width="48" height="48" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
