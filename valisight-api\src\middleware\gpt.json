{"name": "Backend Module Generator", "description": "Generate modular Express.js backend components with strict adherence to coding standards, folder structure, naming conventions, and validation logic.", "instructions": {"overview": "Generate backend modules in Express.js using ES6 modules, Joi validation, and JWT-based authentication. The system is based on a controller-service-route architecture with clean and consistent patterns.", "folderStructure": {"controllers": "/controllers/{module}.controller.js", "routes": "/routes/{module}.routes.js", "services": "/services/{module}.service.js", "validations": "/validations/{module}.validation.js"}, "standards": {"language": "JavaScript (ES6 Modules)", "framework": "Express.js", "middleware": ["cors", "compression", "body-parser", "JWT-based authenticate middleware"], "naming": {"variables": "camelCase", "functions": "camelCase", "files": "lowercase with dot notation (e.g., company.controller.js)", "exports": "named exports only"}, "validation": "Use Joi for request validation in a separate validation file", "authentication": "All routes are protected by `authenticate` middleware", "errorHandling": {"format": {"success": false, "statusCode": 400, "message": "Error message"}, "statusCodeFallback": 500}, "response": {"onSuccess": "res.json(data)", "onError": "res.status(s).json({ success: false, statusCode, message })"}}, "controllers": {"useServiceLayer": true, "validateInputs": true, "destructureReq": true, "errorWrapper": true}, "routes": {"prefix": "/api/v1/{module}", "protectAllRoutes": true, "standardRoutes": [{"method": "POST", "path": "/"}, {"method": "GET", "path": "/all"}, {"method": "GET", "path": "/:id"}, {"method": "PUT", "path": "/:id"}, {"method": "DELETE", "path": "/:id"}]}, "validationSchema": {"register": ["name", "fiscal_year_end", "description", "naics", "users"], "getAll": ["page", "pageSize", "search", "sortBy", "sortOrder"], "getOne": ["companyId"], "update": ["userId", "companyId"], "delete": ["userId", "companyId"]}, "extras": {"optionalRoutes": [{"method": "POST", "path": "/:id/upload-file"}], "paginationSupport": true, "sortingSupport": true, "customHeaders": false}, "samplePrompt": "Create a backend module for `ledgerAccount` with create, list, detail, update, delete, and file upload support. Follow strict coding standards and structure using Express, Joi, and ES6 modules. Use authenticate middleware on all routes and include validation."}}