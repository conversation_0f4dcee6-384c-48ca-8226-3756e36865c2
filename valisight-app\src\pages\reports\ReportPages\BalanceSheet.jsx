import { useEffect } from 'react';

const BalanceSheetDashboard = ({
  headerTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  contentSettings = null,
  reportData = null // Add reportData prop to receive API data
}) => {
  // Extract background color from headerTextStyle for table header
  const headerBgColor = headerTextStyle.color || '#20b2aa';

  // Function to format currency values with $ prefix and comma separators
  // Function to format currency values with $ prefix and comma separators
  const formatCurrency = (value) => {
    if (!value || value === '0' || value === 0) return '$0';

    // Convert to number if it's a string
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[,$]/g, '')) : value;

    // Check if the value is a valid number
    if (isNaN(numValue)) return '$0';

    // Handle negative values correctly
    if (numValue < 0) {
      return '-$' + Math.abs(numValue).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }

    // Format positive values with commas and $ prefix
    return '$' + numValue.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const shouldDisplayChart = (chartKey) => {
    if (!contentSettings?.chartSettings) return true; // Default to true if no settings
    return contentSettings.chartSettings[chartKey] === true;
  };

  // Function to transform API data into the required format
  const transformApiData = (apiData) => {
    if (!apiData || !apiData.balanceSheetTableData) {
      return {
        tableData: [],
        dateInfo: {
          current: 'Jan 25',
          previousYear: 'Jan 24',
          previousMonth: 'Dec 24'
        }
      };
    }

    const rawData = apiData.balanceSheetTableData;

    // Extract date information from the first item's keys
    let currentDate = 'Jan 25';
    let previousYear = 'Jan 24';
    let previousMonth = 'Dec 24';

    if (rawData.length > 0) {
      const keys = Object.keys(rawData[0]);

      // Extract current date
      const actualsKey = keys.find(key => key.includes('_Actuals'));
      if (actualsKey) {
        const dateMatch = actualsKey.match(/([A-Za-z]+_\d+)_Actuals/);
        if (dateMatch) {
          currentDate = dateMatch[1].replace('_', ' ');

          // Calculate previous year (same month, year-1)
          const [month, year] = currentDate.split(' ');
          previousYear = `${month} ${parseInt(year) - 1}`;
        }
      }

      // Extract previous month
      const priorMonthKey = keys.find(key => key.includes('_Prior_Month'));
      if (priorMonthKey) {
        const monthMatch = priorMonthKey.match(/([A-Za-z]+_\d+)_Prior_Month/);
        if (monthMatch) {
          previousMonth = monthMatch[1].replace('_', ' ');
        }
      }
    }

    // Group data by accountClassification and then by account_type
    const classificationGroups = rawData.reduce((acc, item) => {
      const classification = item.accountClassification;
      const accountType = item.account_type;

      if (!acc[classification]) {
        acc[classification] = {};
      }
      if (!acc[classification][accountType]) {
        acc[classification][accountType] = [];
      }
      acc[classification][accountType].push(item);
      return acc;
    }, {});

    // Transform grouped data into table format
    const tableData = [];

    Object.keys(classificationGroups).forEach(classification => {
      // Add main category header (Asset, Liability, Equity)
      tableData.push({
        isMainCategory: true,
        category: classification
      });

      const accountTypes = classificationGroups[classification];

      Object.keys(accountTypes).forEach(accountType => {
        // Add sub-category header (account type)
        tableData.push({
          isSubCategory: true,
          category: accountType
        });

        // Determine if variance is negative for styling
        let priorYearVariance = ''
        let priorMonthVariance = ''
        let isNegativePriorYear = ''
        let isNegativePriorMonth = ''
        let currentMonthActualsKey = ''
        let previousYearActualsKey = ''
        let previousYearEndKey = ''

        // Add individual account rows
        accountTypes[accountType].forEach(item => {
          // Check if this is a total row (contains "Total" in account_name)
          const isTotal = item.account_name.toLowerCase().includes('total');

          // Determine if variance is negative for styling
          priorYearVariance = parseFloat(item.Variance_Prior_Year || "0");
          priorMonthVariance = parseFloat(item.Variance_Prior_Month || "0");
          isNegativePriorYear = priorYearVariance < 0;
          isNegativePriorMonth = priorMonthVariance < 0;
          currentMonthActualsKey = `${currentDate.replace(" ", "_")}_Actuals`;
          previousYearActualsKey = `${previousYear.replace(
            " ",
            "_"
          )}_Prior_Year`;
          previousYearEndKey = `${previousMonth.replace(
            " ",
            "_"
          )}_Prior_Month`;

          tableData.push({
            label: item.account_name,
            jan25: formatCurrency(item[currentMonthActualsKey] || '0'),        // 'Total' rows of FY Start month values
            jan25Percent: formatCurrency(item[currentMonthActualsKey] || '0'), //FY Start month values
            jan24: formatCurrency(item[previousYearActualsKey] || '0'),        // Prior FY Start month values
            jan24Percent: formatCurrency(item[`Variance_Prior_Year`] || '0'),     // Prior FY variance
            variance: formatCurrency(item[previousYearEndKey] || '0'),        // Prior FY End month values
            variancePercent: formatCurrency(item[`Variance_Prior_Month`] || '0'), // Prior month variance
            isTotal: isTotal,
            isGrandTotal: item.account_name.toLowerCase().includes('total') &&
              (classification.toLowerCase().includes('asset') ||
                classification.toLowerCase().includes('total')),
            isNegative: isNegativePriorMonth // Use prior month variance for styling
          });
        });

        // Add total row for each account type if needed
        const totalRow = accountTypes[accountType].find(item =>
          item.account_name.toLowerCase().includes('total')
        );

        if (!totalRow && accountTypes[accountType].length > 1) {
          // Calculate totals if not provided in API
          const totals = accountTypes[accountType].reduce(
            (sum, item) => {
              if (!item.account_name.toLowerCase().includes("total")) {
                sum.actuals += parseFloat(item[currentMonthActualsKey] || "0");
                sum.priorYear += parseFloat(
                  item[previousYearActualsKey] || "0"
                );
                sum.priorMonth += parseFloat(item[previousYearEndKey] || "0");
              }
              return sum;
            },
            { actuals: 0, priorYear: 0, priorMonth: 0 }
          );

          // Dynamically find the current YTD Actuals field
          // Find keys for current and previous month actuals and percent of income
          const actualsKey = `${currentDate.replace(" ", "_")}_Actuals`;
          const priorYearKey = `${currentDate.replace(" ", "_")}_Prior_Year`;
          const priorMonthKey = `${currentDate.replace(" ", "_")}_Prior_Month`;
          //  Variance_Prior_Month`;
          // Variance_Prior_Year`;

          tableData.push({
            label: `Total ${accountType}`,
            jan25: formatCurrency(totals[actualsKey]),
            jan25Percent: formatCurrency(totals[priorYearKey]),
            jan24: formatCurrency(totals[priorMonthKey]),
            jan24Percent: formatCurrency(totals[priorMonthKey]),
            variance: formatCurrency(totals[priorMonthKey]),
            variancePercent: formatCurrency(totals[priorMonthKey]),
            isTotal: true,
            isNegative: totals.actuals - totals.priorMonth < 0,
          });
        }
      });
    });

    return {
      tableData,
      dateInfo: {
        current: currentDate,
        previousYear: previousYear,
        previousMonth: previousMonth
      }
    };
  };

  useEffect(() => {
    if (shouldDisplayChart('balanceSheet')) {
      transformApiData(reportData);
    }
  }, [reportData, contentSettings]);

  // Transform the API data
  const { tableData, dateInfo } = transformApiData(reportData);

  // Fallback to empty state if no data
  if (!reportData || !reportData.balanceSheetTableData || tableData.length === 0) {
    return (
      <div className="p-5">
        <div className="max-w-6xl bg-white p-10 mx-auto overflow-x-auto">
          <div>
            <h2 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              Balance Sheet
            </h2>
            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              Balance Sheet Report
            </div>
            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black", fontSize: "20px" }}>
              (reportData?.companyName)
            </div>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-lg text-gray-600">No data available</div>
              <div className="text-sm text-gray-500 mt-2">Please check your data source</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderTableRow = (item, index) => {
    if (item.isMainCategory) {
      return (
        <tr key={index}>
          <td className="text-left pl-2 font-bold text-gray-800" style={{ ...contentTextStyle, fontSize: '15px' }}>
            {item.category}
          </td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
        </tr>
      );
    }

    if (item.isSubCategory) {
      return (
        <tr key={index}>
          <td className="text-left pl-4 font-semibold text-gray-700" style={{ ...contentTextStyle, fontSize: '15px' }}>
            {item.category}
          </td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
        </tr>
      );
    }

    if (item.isAccountGroup) {
      return (
        <tr key={index}>
          <td className="text-left pl-6 font-medium text-gray-600" style={{ ...contentTextStyle, fontSize: '15px' }}>
            {item.category}
          </td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
        </tr>
      );
    }

    if (item.isGrandTotal) {
      return (
        <tr key={index} className="border-t-2 border-gray ">
          <td className="text-left pl-2 font-bold text-gray-900" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.label}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan25}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan24}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan24Percent}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.variance}</strong>
          </td>
          <td className="text-right font-bold" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.variancePercent}</strong>
          </td>
        </tr>
      );
    }

    if (item.isTotal) {
      return (
        <tr key={index} className="border-t-2 border-gray">
          <td className="text-left pl-6 font-semibold" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.label}</strong>
          </td>
          <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan25}</strong>
          </td>
          <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan24}</strong>
          </td>
          <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan24Percent}</strong>
          </td>
          <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.variance}</strong>
          </td>
          <td
            className={`text-right ${item.isNegative ? 'text-red-600' : ''}`}
            style={{ ...contentTextStyle, fontSize: '15px' }}
          >
            <strong>{item.variancePercent}</strong>
          </td>
        </tr>
      );
    }

    return (
      <tr key={index}>
        <td className="text-left pl-8 font-normal" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.label}
        </td>
        <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan25Percent}
        </td>
        <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan24}
        </td>
        <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan24Percent}
        </td>
        <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.variance}
        </td>
        <td
          className={`text-right ${item.isNegative ? 'text-red-600' : ''}`}
          style={{ ...contentTextStyle, fontSize: '15px' }}
        >
          {item.variancePercent}
        </td>
      </tr>
    );
  };

  // Generate dynamic date description
  const getDateDescription = () => {
    if (dateInfo.current) {
      const [month, year] = dateInfo.current.split(' ');
      const monthNames = {
        'Jan': 'January', 'Feb': 'February', 'Mar': 'March', 'Apr': 'April',
        'May': 'May', 'Jun': 'June', 'Jul': 'July', 'Aug': 'August',
        'Sep': 'September', 'Oct': 'October', 'Nov': 'November', 'Dec': 'December'
      };
      const fullMonth = monthNames[month] || month;
      return `As of ${fullMonth} 31st, 20${year}`;
    }
    return 'As of January 31st, 2025';
  };

  return (
    (shouldDisplayChart('balanceSheet')) ? (
      <div className="min-h-screen p-5 balance-sheet-component">
        {/* Main Container */}
        <div className="max-w-6xl bg-white p-10 mx-auto overflow-x-auto relative min-h-screen">

          {/* Header Section */}
          <div>
            <h2
              style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}
            >
              Balance Sheet
            </h2>
            <div
              style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}
            >
              {getDateDescription()}
            </div>
            <div
              style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black", fontSize: "20px" }}
            >
              {reportData?.companyName}
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto pb-32">
            <table className="w-full border-collapse text-sm mt-4">
              <thead>
                {/* Month Header Row */}
                <tr className="text-black text-center bg-white">
                  <th
                    className="text-left bg-white border-0"
                    style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                  ></th>
                  <th
                    className="text-left bg-white border-0 pl-10"
                    style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                    colSpan="1"
                  >
                    {dateInfo.current}
                  </th>
                  <th
                    className="text-left bg-white border-0 pl-10"
                    style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                    colSpan="2"
                  >
                    {dateInfo.previousYear}
                  </th>
                  <th
                    className="text-left bg-white border-0 pl-10"
                    style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                    colSpan="2"
                  >
                    {dateInfo.previousMonth}
                  </th>
                </tr>
                {/* Column Header Row */}
                <tr>
                  <th
                    className="text-right text-white p-2 font-bold text-sm"
                    style={{
                      ...contentTextStyle,
                      fontSize: '15px',
                      color: 'white'
                    }}
                  ></th>
                  {/* Current Period */}
                  <th
                    className="text-center text-white  font-bold text-sm"
                    style={{
                      backgroundColor: headerBgColor,
                      ...contentTextStyle,
                      fontSize: '15px',
                      color: 'white',
                      borderRight: '20px solid white'
                    }}
                  >
                    Actuals
                  </th>

                  {/* Prior Year Group */}
                  <th
                    className="text-center text-white  font-bold text-sm"
                    style={{
                      backgroundColor: headerBgColor,
                      ...contentTextStyle,
                      fontSize: '15px',
                      color: 'white'
                    }}
                  >
                    Prior Year
                  </th>
                  <th
                    className="text-center text-white font-bold text-sm"
                    style={{
                      backgroundColor: headerBgColor,
                      ...contentTextStyle,
                      fontSize: '15px',
                      color: 'white',
                      borderRight: '20px solid white'
                    }}
                  >
                    <div>Variance</div>
                  </th>

                  {/* Prior Month Group */}
                  <th
                    className="text-center text-white font-bold text-sm"
                    style={{
                      backgroundColor: headerBgColor,
                      ...contentTextStyle,
                      fontSize: '15px',
                      color: 'white'
                    }}
                  >
                    Prior Month
                  </th>
                  <th
                    className="text-center text-white font-bold text-sm"
                    style={{
                      backgroundColor: headerBgColor,
                      ...contentTextStyle,
                      fontSize: '15px',
                      color: 'white'
                    }}
                  >
                    Variance
                  </th>
                </tr>
              </thead>
              <tbody>
                {tableData.map((item, index) => renderTableRow(item, index))}
              </tbody>
            </table>
          </div>
          <div
            className='text-center text-slate-300 text-xs py-9 px-12 mt-auto'
            style={{ position: 'absolute', left: 0, right: 0, bottom: 0 }}
          >
            <p>
              The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice
              contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any
              information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or
              delivered information.
            </p>
          </div>
        </div>
      </div>
    ) : null
  );
};

export default BalanceSheetDashboard;