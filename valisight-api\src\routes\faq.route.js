import { Router } from 'express';
export const faqRoute = Router();
import * as faqController from '../controllers/faq.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

faqRoute.post('/', faqController.createFaq);
faqRoute.get('/', authenticate, faqController.getFaqs);
faqRoute.get('/all', faqController.getFaqs); //for admin we will not give token to fetch all
faqRoute.delete('/:ids', faqController.deleteFaq);
faqRoute.put('/:id', faqController.updateFaq);