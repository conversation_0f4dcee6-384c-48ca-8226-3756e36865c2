services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    ports:
      - "3000:3000"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`stage.nowanalytics.fintuition.ai`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=myresolver"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"  # Port inside the container where the app is running
    networks:
      - traefik_network

networks:
  traefik_network:
    external: true